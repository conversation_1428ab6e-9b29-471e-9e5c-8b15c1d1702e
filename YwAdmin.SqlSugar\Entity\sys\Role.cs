

namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 用户
/// </summary>
[SugarTable("SYS_ROLE")]
public partial class Role : BaseEntity
{
    /// <summary>
    /// 角色代码
    /// </summary>
    [SugarColumn(ColumnName = "ROLECODE")]
    public string RoleCode { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    [SugarColumn(ColumnName = "ROLENAME")]
    public string RoleName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "REMARK")]
    public string Remark { get; set; }

    /// <summary>
    /// 描述
    /// </summary>

    [SugarColumn(ColumnName = "DESCRIPTION")]
    public string Description { get; set; }
}