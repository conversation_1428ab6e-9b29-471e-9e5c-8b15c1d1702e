﻿

using Microsoft.Extensions.DependencyInjection;

using YwAdmin.Core;
using YwAdmin.SqlSugar;

using Volo.Abp;
using Volo.Abp.Modularity;
using Volo.Abp.Timing;

namespace YwAdmin.Zero;
[DependsOn(typeof(AdminSqlSugarModule), typeof(AdminCoreModule))]
public class AdminZeroModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddSingleton<IClock, Clock>();
    }
    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {

    }
}
