namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 采购订单表
/// </summary>
[SugarTable("PURCHASE_ORDER")]
public partial class PurchaseOrderEntity : BaseEntity
{
    /// <summary>
    /// 订单号
    /// </summary>
    public string OrderNo { get; set; }

    /// <summary>
    /// 订单时间
    /// </summary>
    public DateTime OrderDate { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public string OrderType { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public string OrderStatus { get; set; }

    /// <summary>
    /// 采购订单号
    /// </summary>
    public string PoNo { get; set; }

    /// <summary>
    /// 报关单号
    /// </summary>
    public string DeclarationNo { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string Supplier { get; set; }

    /// <summary>
    /// 入库时间
    /// </summary>
    public DateTime? ReceiptDate { get; set; }

    /// <summary>
    /// 入库人ID
    /// </summary>
    public  long? ReceiptUserId { get; set; }

    /// <summary>
    /// 确认人ID
    /// </summary>
    public long? ConfirmUserId { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmDateTime { get; set; }

     /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}