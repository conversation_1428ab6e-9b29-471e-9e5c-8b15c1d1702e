﻿using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Options;
using YwAdmin.Api.Host;
using YwAdmin.Api.Host.Options;
using YwAdmin.Multiplex.AdminUser;
using YwAdmin.Multiplex.Contracts.IAdminUser;

var builder = WebApplication.CreateBuilder(args);

builder.Host.UseAutofac();

builder.Services.ReplaceConfiguration(builder.Configuration);

builder.Services.AddApplication<AdminHostModule>();

builder.Services.AddScoped<ICurrentUser, CurrentUser>();

var limitSizeMB = builder.Configuration.GetValue<int>("FileStorage:MaxFileSize");
var storagePath = builder.Configuration.GetValue<string>("FileStorage:Path");
//限制文件大小
builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = limitSizeMB * 1024 * 1024; // 限制文件大小为 5MB
});
// 同时配置Kestrel（必须添加）
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.Limits.MaxRequestBodySize = limitSizeMB * 1024 * 1024;
});

// 添加 CORS 服务
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins", builder =>
    {
        builder.WithOrigins("http://localhost:5777") // 允许的前端地址
               .WithOrigins("http://localhost:4173")
               .AllowAnyHeader() // 允许任何请求头
               .AllowAnyMethod() // 允许任何请求方法
               .AllowCredentials(); // 如果需要传递 Cookie 或其他凭据
    });
});
var app = builder.Build();
// 使用请求本地化中间件
var localizationOptions = app.Services.GetRequiredService<IOptions<RequestLocalizationOptions>>().Value;
app.UseRequestLocalization(localizationOptions);
app.UseMiddleware<LanguageMiddleware>();

// 使用转发头中间件，必须在其他中间件之前使用
app.UseCustomForwardedHeaders();

// 使用 CORS 中间件
app.UseCors("AllowSpecificOrigins");

// 配置静态文件服务，允许访问 wwwroot 和 uploads 目录中的文件
app.UseStaticFiles(); // 默认支持 wwwroot

// 配置静态文件服务，允许访问 uploads 目录中的文件
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(storagePath), // 指定 uploads 目录
    RequestPath = "/files" // 映射为 /uploads 路径
});

app.InitializeApplication();
app.Run();