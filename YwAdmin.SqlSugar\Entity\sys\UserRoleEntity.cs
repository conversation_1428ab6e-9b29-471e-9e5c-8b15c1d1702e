

using YwAdmin.SqlSugar;

namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 用户角色
/// </summary>
[SugarTable("SYS_USER_ROLE")]
public partial class UserRoleEntity : BaseEntity
{
    /// <summary>
    /// 角色ID
    /// </summary>
    [SugarColumn(ColumnName = "ROLE_ID")]
    public long RoleId { get; set; }
    /// <summary>
    /// 用户ID
    /// </summary>
    [SugarColumn(ColumnName = "USER_ID")]
    public long UserId { get; set; }
}