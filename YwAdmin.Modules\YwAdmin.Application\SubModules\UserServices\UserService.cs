using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using YwAdmin.Application.CurrentUserContext;
using YwAdmin.Application.SubModules.UserServices.Dtos;
using YwAdmin.Application.UserServices.Dtos;
using YwAdmin.Core.DataEncryption.Encryptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using YwAdmin.Core.Extensions;



namespace YwAdmin.Application.UserServices
{
    /// <summary>
    /// 用户服务
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
    public class UserService(ISqlSugarClient db, Repository<UserEntity> userRepository,
        ICurrentUserContext context,
        IWebHostEnvironment env,
        IHttpContextAccessor httpContextAccessor,
        IConfiguration config,
        ILogger<UserService> logger
        ) : ApplicationServiceBase
    {
        private readonly ISqlSugarClient _db = db;
        private readonly Repository<UserEntity> _userRepository = userRepository;
        private readonly ICurrentUserContext _context = context;
        private readonly IWebHostEnvironment _env = env;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly IConfiguration _config = config;
        private readonly ILogger<UserService> _logger = logger;
        /// <summary>
        /// 新增用户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// 
        [AllowAnonymous]
        public async Task<TableData> InsertUser(AddUserInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Account))
                {
                    throw new UserFriendlyException("请输入账号！");
                }



                input.Password = "123456";
                var entity = input.Adapt<UserEntity>();
                if (input.Id != null)
                {
                    var userInfo = await _userRepository.GetByIdAsync(input.Id);
                    entity.Password = userInfo.Password;
                    entity.Status = userInfo.Status;
                    await _userRepository.UpdateAsync(entity);
                }
                else
                {

                    var userInfo = await _db.Queryable<UserEntity>().Where(x => x.Account == input.Account).FirstAsync();
                    if (userInfo != null)
                    {
                        throw new UserFriendlyException("账号重复，请重新输入！");
                    }
                    //密码初始化123456
                    string passWord = "123456";
                    entity.Password = MD5Encryption.Encrypt(passWord);
                    entity.CreateUserId = _context.GetUserId();
                    //entity.Id = 2;
                    var id = await _userRepository.InsertReturnSnowflakeIdAsync(entity);
                    _ = await _db.Insertable(new UserRoleEntity
                    {
                        UserId = id,
                        RoleId = 1,
                    }).ExecuteReturnSnowflakeIdAsync();
                }

                return new TableData
                {
                    Message = "保存成功！"
                };
            }
            catch (Exception exc)
            {
                return new TableData { Code = 500, Message = exc.Message };
            }

        }

        /// <summary>
        /// 查询用户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task<TableData> GetUserList(GetPagedListInput input)
        {
            var query = _db.Queryable<UserEntity>()
           .WhereIF(!input.Name.IsNullOrEmpty(), (u) => u.Name.Contains(input.Name))
           .WhereIF(!input.Account.IsNullOrEmpty(), (u) => u.Account.Contains(input.Account, StringComparison.CurrentCultureIgnoreCase))
           .WhereIF(!input.Telephone.IsNullOrEmpty(), (u) => u.Telephone.Contains(input.Telephone))
           .WhereIF(!input.Email.IsNullOrEmpty(), (u) => u.Email.Contains(input.Email))
           .WhereIF(input.Status.HasValue, (u) => u.Status == input.Status)
           .Select(u => new UserEntity
           {
               Id = u.Id.SelectAll()
           }).OrderBy(u => u.Status);
            query = query.ApplyFilters(input.Filters);
            var tatalCount = await query.CountAsync();
            var userList = await query
                .InputPage(input)
                .ToListAsync();

            var result = userList.Adapt<List<UserOutput>>().Select(user =>
            {
                user.OrganizationName = _db.Queryable<OrganizationEntity>().First(x => x.Id == user.OrganizationId)?.Name; ;
                return user;
            }).ToList();
            //.OrderByDescending(u => u.CreateTime)
            //.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
            //return pagedList.Adapt<PagedList<UserOutput>>();
            return new TableData
            {
                Data = result,
                Message = "查询成功！",
                TotalCount = tatalCount
            };

        }

        [HttpPost]
        public async Task<TableData> SetAccountStatus(PutUserInput input)
        {
            try
            {
                var user = await _userRepository.GetFirstAsync(x => x.Id == input.Id);
                if (input.Status == 0)
                {
                    //启用
                    user.Status = 1;
                }
                else if (input.Status == 1)
                {
                    //禁用
                    user.Status = 0;
                }
                await _userRepository.UpdateAsync(user);
                return new TableData
                {
                    Message = L["UpdateSuccessful"]
                };
            }
            catch (Exception exc)
            {
                throw new UserFriendlyException(L["Abnormal"]);
            }

        }

        /// <summary>
        /// 更新用户头像
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<TableData> SetUserAvatar(UserInput input)
        {
            try
            {
                if (input.Id == null)
                {
                    throw new UserFriendlyException(L["Abnormal"]);
                }
                var userInfo = await _userRepository.GetFirstAsync(x => x.Id == input.Id);
                userInfo.Avatar = input.Avatar;
                await _userRepository.UpdateAsync(userInfo);
                return new TableData
                {
                    Message = L["UpdateSuccessful"]
                };
            }
            catch (Exception)
            {

                throw new UserFriendlyException(L["Abnormal"]);
            }

        }
        [HttpPost]
        public async Task<TableData> UploadAvatar(IFormFile file)
        {
            // 1. 从配置读取基础路径（确保末尾无斜杠）
            var basePath = _config["FileStorage:Path"]?.TrimEnd('\\', '/');
            if (string.IsNullOrEmpty(basePath))
            {
                throw new UserFriendlyException("文件存储路径未配置");
            }

            // 2. 检查文件是否为空
            if (file == null || file.Length == 0)
            {
                throw new UserFriendlyException(L["Abnormal"]);
            }

            // 3. 文件类型检查
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            if (!allowedExtensions.Contains(fileExtension))
            {
                throw new UserFriendlyException(L["Abnormal"]);
            }

            // 4. 定义虚拟访问路径和物理存储路径
            var virtualPath = "files/uploads/images/avatars"; // IIS 中配置的虚拟路径
            var physicalPath = Path.Combine(basePath, "uploads", "images", "avatars");

            // 5. 确保目录存在
            Directory.CreateDirectory(physicalPath);

            // 6. 生成唯一文件名
            var fileName = $"{Guid.NewGuid()}{fileExtension}";
            var filePhysicalPath = Path.Combine(physicalPath, fileName);

            // 7. 保存文件
            using (var stream = new FileStream(filePhysicalPath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // 8. 返回可直接访问的 URL（无需拼接 Host，浏览器会自动补全）
            var fileUrl = $"{virtualPath}/{fileName}";

            return new TableData
            {
                Data = new { url = fileUrl } // 返回相对路径，如 "/files/uploads/images/avatars/xxx.jpg"
            };
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<TableData> GetAvatar(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    throw new UserFriendlyException("头像文件名不能为空");
                }
                var basePath = _config["FileStorage:Path"]?.TrimEnd('\\', '/');
                var uploadPath = Path.Combine(basePath, "uploads", "images", "avatars");
                var filePath = Path.Combine(uploadPath, fileName);
                // 获取当前用户ID
                //var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                //if (string.IsNullOrEmpty(userId))
                //{
                //    return Unauthorized();
                //}

                // 验证文件扩展名
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var extension = Path.GetExtension(fileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(extension))
                {
                    throw new UserFriendlyException("不支持的文件类型");
                }


                // 检查文件是否存在
                if (!System.IO.File.Exists(filePath))
                {
                    throw new UserFriendlyException("头像文件不存在");
                }

                // 读取文件内容
                var avatarBytes = await System.IO.File.ReadAllBytesAsync(filePath);

                // 获取文件扩展名对应的MIME类型
                var contentType = extension switch
                {
                    ".png" => "image/png",
                    ".jpg" or ".jpeg" => "image/jpeg",
                    ".gif" => "image/gif",
                    _ => "application/octet-stream"
                };

                // 返回文件流
                return new TableData
                {
                    Data = new { FileBytes = avatarBytes, ContentType = contentType }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取头像失败，文件名: {FileName}", fileName);
                throw new UserFriendlyException("获取头像失败");
            }
        }


        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        [HttpPost]
        public async Task<TableData> UpdateUserPsw(UserInput input)
        {
            if (input.Id == null)
            {
                throw new UserFriendlyException(L["Abnormal"]);
            }
            var oldPsw = MD5Encryption.Encrypt(input.Password);
            var userInfo = await _userRepository.GetFirstAsync(x => x.Id == input.Id && x.Password == oldPsw);
            if (userInfo == null)
            {
                throw new UserFriendlyException(L["OldPswFail"]);
            }
            if (input.ConfirmPassword != input.NewPassword)
            {
                throw new UserFriendlyException(L["PswNewNotOld"]);
            }
            userInfo.Password = MD5Encryption.Encrypt(input.ConfirmPassword);
            await _userRepository.UpdateAsync(userInfo);
            return new TableData
            {
                Message = L["UpdateSuccessful"]
            };
        }

    }
};

