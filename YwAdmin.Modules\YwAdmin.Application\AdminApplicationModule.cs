﻿using System.Reflection;
using YwAdmin.Core.Mapster;
using YwAdmin.Multiplex;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.Validation.Localization;
using Volo.Abp.VirtualFileSystem;
using YwAdmin.Application.Localization;

namespace YwAdmin.Application
{
    [DependsOn(typeof(AdminMultiplexModule), typeof(AbpLocalizationModule))]
    public class AdminApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.AddEmbedded<AdminApplicationModule>("YwAdmin.Application");
            });
            // 注册 Mapster
            context.Services.AddMapsterIRegister(Assembly.GetExecutingAssembly());
            Configure<AbpAspNetCoreMvcOptions>(options =>
            {
                options.ConventionalControllers.Create(typeof(AdminApplicationModule).Assembly, opts =>
                {
                    opts.RootPath = "v1";
                    opts.UrlActionNameNormalizer = (action) =>
                    {
                        // 确保动作名称不被修改
                        return action.ActionNameInUrl;
                    };
                });
            });
            Configure<AbpLocalizationOptions>(options =>
            {
                options.DefaultResourceType = typeof(ApplicationResource);
                options.Resources
                  .Add<ApplicationResource>("en") //Define the resource by "en" default culture
                  .AddBaseTypes(typeof(AbpValidationResource)) //Inherit from an existing resource
                  .AddVirtualJson("/Localization/Application"); //Add strings from virtual json files
                options.Languages.Add(new LanguageInfo("en", "English", "famfamfam-flag-england")); //英文
                options.Languages.Add(new LanguageInfo("zh-Hans", "简体中文", "famfamfam-flag-cn")); // 简体中文
                options.Languages.Add(new LanguageInfo("vi", "Tiếng Việt", "famfamfam-flag-vn")); // 越南文
            });
        }
    }
}