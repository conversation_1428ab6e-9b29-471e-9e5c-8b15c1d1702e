﻿

using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;

namespace YwAdmin.Core.Signalr;
public static class PurestSignalrExtensions
{
    public static IServiceCollection AddPurestSignalr(this IServiceCollection services)
    {
        services.AddSingleton<IUserIdProvider, PurestUserIdProvider>();
        services.AddSignalR().AddNewtonsoftJsonProtocol(options =>
        {
            options.PayloadSerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
        });
        return services;
    }
}
