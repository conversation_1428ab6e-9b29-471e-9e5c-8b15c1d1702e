using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using YwAdmin.Application.CurrentUserContext;
using Microsoft.Extensions.Configuration;
using YwAdmin.Multiplex.Contracts.Dto;
using YwAdmin.SqlSugar.Entity.Basic;
using NUglify;


namespace YwAdmin.Application.BasicServices
{
    /// <summary>
    /// 用户服务
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
    public class BasicService(ISqlSugarClient db, Repository<UserEntity> userRepository,
        ICurrentUserContext context,
        IWebHostEnvironment env,
        IHttpContextAccessor httpContextAccessor,
        IConfiguration config
        ) : ApplicationServiceBase
    {
        private readonly ISqlSugarClient _db = db;
        private readonly Repository<UserEntity> _userRepository = userRepository;
        private readonly ICurrentUserContext _context = context;
        private readonly IWebHostEnvironment _env = env;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly IConfiguration _config = config;

        [HttpPost]
        public async Task<TableData> GetDropDownList(DropDownDto input)
        {
            if (string.IsNullOrEmpty(input.CodeType))
            {
                return new TableData { Data = new List<DropDownDto>() };
            }
            var resultList = _db.Queryable<DictionaryCodeType, DictionaryCode>((codeType, dict) => codeType.CodeType == dict.CodeType)
               .Where((codeType, dict) => codeType.CodeType.Equals(input.CodeType) && dict.IsEnable.Equals(1)) // 根据输入条件过滤
               .Select((codeType, dict) => new DropDownDto
               {
                   Label = dict.Text, // 假设 Dictionary 表中有一个 Name 字段
                   Value = dict.Value // 假设 Dictionary 表中有一个 Value 字段
               })
               .ToList();
            return new TableData { Data = resultList };
        }


        [HttpPost]
        public async Task<TableData> GetWarehouseList(DropDownDto input)
        {
            var query = _db.Queryable<Warehouse>().Where(x => x.Status == 1);
            if (!string.IsNullOrEmpty(input.CodeType))
            {
                query = query.Where(x => x.TypeCode == input.CodeType);
            }
            var resultList = query.ToList();
            return new TableData { Data = resultList };
        }
        

    }
};

