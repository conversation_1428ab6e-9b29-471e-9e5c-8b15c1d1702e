{
  "AllowedHosts": "*",
  "SnowflakeIdOptions": {
    "WorkId": "1"
  },
  "FileStorage": {
    "MaxFileSize": 20, //单文件最大值M
    "Path": "E:/YW" //为空时,默认当前程序运行目录/FileSystem下
  },
  "EnableGlobalUnitOfWork": true,
  "JwtOptions": {
    "SecretKey": "49BA59ABBE56E05749BA59ABBE56E057", // JWT签名密钥，用于生成和验证Token的安全密钥
    "ExpiredMinutes": 14400, // Token过期时间（分钟），设置Token的有效期限
    "RefreshMinutes": 60, // Token刷新时间（分钟），当Token剩余有效期小于此值时自动刷新，如果为0则取消无感刷新
    "Issuer": "YwAdmin", // Token发行者，用于验证Token的来源
    "Audience": "YwAdmin.Client", // Token接收者，用于验证Token的目标受众
    "EnableBlacklist": true, // 是否启用Token黑名单功能，启用后可以使已签发的Token失效
    "BlacklistExpiryMinutes": 1440, // 黑名单中Token的过期时间（分钟），默认为24小时
    "ValidateDeviceInfo": false, // 是否验证设备信息，启用后Token将绑定到特定设备，增强安全性
    "EnableJti": true, // 是否启用JWT ID，为每个Token生成唯一标识，防止重放攻击
    "EnableAutoLogout": true, // 是否启用自动登出功能，长时间不活跃的用户将被自动登出
    "AutoLogoutMinutes": 600 // 自动登出时间（分钟），用户在此时间内无活动将被自动登出
  },
  //"ConnectionOptions": {
  //  "DbType": "Mysql",
  //  "ConnectionString": "Data Source=localhost;Database=wzwtpV1;User ID=sa;Password=******;pooling=true;port=3306;sslmode=none;CharSet=utf8;AllowPublicKeyRetrieval=True;"
  //},
  "ConnectionOptions": {
    "DbType": "SqlServer",
    "ConnectionString": "Data Source=**************,1433;Initial Catalog=YWV1;User=sa;Password=********;"
  },
  //"OAuth2Options": [
  //  {
  //    "Name": "gitee",
  //    "ClientId": "ClientId",
  //    "ClientSecret": "ClientSecret",
  //    "RedirectUri": "http://************:8848/oauth-callback"
  //  },
  //  {
  //    "Name": "github",
  //    "ClientId": "ClientId",
  //    "ClientSecret": "ClientSecret",
  //    "RedirectUri": "http://************:8848/oauth-callback"
  //  }
  //]
  "EPPlus": {
    "ExcelPackage": {
      "LicenseContext": "NonCommercial",
      "Compatibility": {
        "IsWorksheets1Based": "true"
      }
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "System": "Warning"
    }
  }
}