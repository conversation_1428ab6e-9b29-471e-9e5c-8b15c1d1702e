﻿

using System.Security.Claims;

namespace YwAdmin.Multiplex.Contracts.IAdminUser;

public interface IAdminToken
{
    /// <summary>
    /// 生成token字符串
    /// </summary>
    /// <param name="claims">用户声明</param>
    /// <returns>JWT Token字符串</returns>
    string GenerateTokenString(IEnumerable<Claim> claims);

    /// <summary>
    /// 返回刷新分钟数
    /// </summary>
    /// <returns>刷新分钟数</returns>
    double GetRefreshMinutes();

    /// <summary>
    /// 验证Token
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>验证结果，成功返回true，失败返回false</returns>
    Task<bool> ValidateTokenAsync(string token);

    /// <summary>
    /// 使Token失效（加入黑名单）
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>操作结果</returns>
    Task<bool> RevokeTokenAsync(string token);
}