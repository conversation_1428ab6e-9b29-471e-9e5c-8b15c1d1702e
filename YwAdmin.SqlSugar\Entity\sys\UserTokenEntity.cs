

namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 用户Token
/// </summary>
[SugarTable("SYS_USER_TOKEN")]
public class UserTokenEntity : BaseEntity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [SugarColumn(ColumnName = "USER_ID", ColumnDescription = "用户ID")]
    public long UserId { get; set; }

    /// <summary>
    /// Token值
    /// </summary>
    [SugarColumn(ColumnName = "TOKEN_VALUE", ColumnDescription = "Token值", Length = 1000)]
    public string TokenValue { get; set; }

    /// <summary>
    /// 刷新Token
    /// </summary>
    [SugarColumn(ColumnName = "REFRESH_TOKEN", ColumnDescription = "刷新Token", Length = 1000)]
    public string RefreshToken { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    [SugarColumn(ColumnName = "EXPIRE_TIME", ColumnDescription = "过期时间")]
    public DateTime ExpireTime { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    [SugarColumn(ColumnName = "LAST_ACTIVITY_TIME", ColumnDescription = "最后活动时间")]
    public DateTime LastActivityTime { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_INFO", ColumnDescription = "设备信息", Length = 500)]
    public string DeviceInfo { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    [SugarColumn(ColumnName = "IP_ADDRESS", ColumnDescription = "IP地址", Length = 50)]
    public string IpAddress { get; set; }

    /// <summary>
    /// 用户代理
    /// </summary>
    [SugarColumn(ColumnName = "USER_AGENT", ColumnDescription = "用户代理", Length = 500)]
    public string UserAgent { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    [SugarColumn(ColumnName = "IS_ACTIVE", ColumnDescription = "是否有效")]
    public bool IsActive { get; set; }
}
