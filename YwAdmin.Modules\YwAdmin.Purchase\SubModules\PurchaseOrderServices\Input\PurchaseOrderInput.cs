// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.PurchaseOrder.PurchaseOrderServices.Input;

/// <summary>
/// PurchaseOrder表输入模型
/// </summary>
public class PurchaseOrderInput : PaginationParams
{

    /// <summary>
    /// 订单号
    /// </summary>
    public string OrderNo { get; set; }

    /// <summary>
    /// 订单时间
    /// </summary>
    public DateTime Orderdate { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public string OrderType { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public string OrderStatus { get; set; }

    /// <summary>
    /// 采购订单号
    /// </summary>
    public string PoNo { get; set; }

    /// <summary>
    /// 报关单号
    /// </summary>
    public string Declarationno { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string Supplier { get; set; }

    /// <summary>
    /// 入库时间
    /// </summary>
    public DateTime? Receiptdate { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    protected override Dictionary<string, string> PrefixMap { get; } = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
    {

    };
}