




CREATE TABLE [dbo].[SYS_DICTIONARY_CODE] (
  [Id] int  NOT NULL IDENTITY(1,1),
  [Value] nvarchar(40) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Text] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Seq] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NULL,
  [IsEnable] bit  NOT NULL,
  [IsDefault] bit  NOT NULL,
  [Description] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [CodeType] nvarchar(40) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [CodeTypeName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [LastModificationTime] datetime DEFAULT getdate() NULL,
  [LastModifierUserId] bigint  NULL,
  [CreationTime] datetime  NOT NULL,
  [CreatorUserId] bigint  NULL,
CONSTRAINT [PK_dbo.SYS_DICTIONARY_CODE] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SYS_DICTIONARY_CODE] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'值字段',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'Value'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文本字段',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'Text'
GO

EXEC sp_addextendedproperty
'MS_Description', N'序列',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'Seq'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否启用',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'IsEnable'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否默认',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'IsDefault'
GO

EXEC sp_addextendedproperty
'MS_Description', N'描述字段',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'代码类型',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'CodeType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'代码类型名称',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODE',
'COLUMN', N'CodeTypeName'
GO


