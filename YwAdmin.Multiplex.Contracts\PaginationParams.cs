﻿

using System.ComponentModel.DataAnnotations;

namespace YwAdmin.Multiplex.Contracts;
public class PaginationParams
{
    public long? Id { get; set; }
    /// <summary>
    /// 页码
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "页码只能在 1 到 2147483647 之间")]
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页容量
    /// </summary>
    [Range(5, 200, ErrorMessage = "每页容量只能在 5 到 200 之间")]
    public int PageSize { get; set; } = 10;


    private List<FilterCondition>? _filtersCache;

    public List<FilterCondition> Filters
    {
        get
        {
            if (_filtersCache == null)
                _filtersCache = new List<FilterCondition>();

            if (_filtersCache.Count == 0)
                _filtersCache = BuildFiltersWithPrefix();
            return _filtersCache;
        }
        set => _filtersCache = value;
    }

    //public List<FilterCondition> Filters { get; set; }

    protected virtual Dictionary<string, string> PrefixMap { get; } = new Dictionary<string, string>();

    protected virtual List<FilterCondition> BuildFiltersWithPrefix()
    {
        var filters = new List<FilterCondition>();
        
        if (_filtersCache != null)
        {
            foreach (var filter in _filtersCache)
            {
                var newFilter = new FilterCondition
                {
                    Column = filter.Column,
                    FilterType = filter.FilterType,
                    Value = filter.Value,
                    Operator = filter.Operator
                };

                // 如果列名不包含点且在前缀映射中，添加前缀
                if (!filter.Column.Contains('.') && PrefixMap.TryGetValue(filter.Column, out var prefix))
                {
                    newFilter.Column = $"{prefix}.{filter.Column}";
                }

                filters.Add(newFilter);
            }
        }

        return filters;
    }

    private List<SortModel>? _sortModelsCache;

    public List<SortModel> SortModel
    {
        get
        {
            if (_sortModelsCache == null)
                _sortModelsCache = new List<SortModel>();

            if (_sortModelsCache.Count == 0)
                _sortModelsCache = BuildSortModelsWithPrefix();
            return _sortModelsCache;
        }
        set => _sortModelsCache = value;
    }

    protected virtual List<SortModel> BuildSortModelsWithPrefix()
    {
        var sortModels = new List<SortModel>();
        
        if (_sortModelsCache != null)
        {
            foreach (var sort in _sortModelsCache)
            {
                var newSort = new SortModel
                {
                    Field = sort.Field,
                    Direction = sort.Direction,
                    Priority = sort.Priority
                };

                // 如果字段名不包含点且在前缀映射中，添加前缀
                if (!sort.Field.Contains('.') && PrefixMap.TryGetValue(sort.Field, out var prefix))
                {
                    newSort.Field = $"{prefix}.{sort.Field}";
                }

                sortModels.Add(newSort);
            }
        }

        return sortModels;
    }

}
