namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 成品表
/// </summary>
[SugarTable("Items")]
public partial class Items : BaseEntity
{
    /// <summary>
    /// 成品编号
    /// </summary>
    public string FGCode { get; set; }

    /// <summary>
    /// 席次
    /// </summary>
    public string Seat { get; set; }

    /// <summary>
    /// 系列号
    /// </summary>
    public string SeriesNo { get; set; }

    /// <summary>
    /// 款号
    /// </summary>
    public string StyleNo { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string VersionNo { get; set; }

    /// <summary>
    /// 面向地
    /// </summary>
    public string TargetMarket { get; set; }

    /// <summary>
    /// 单品重量KG
    /// </summary>
    public decimal? UnitWeightKg { get; set; }

    /// <summary>
    /// 产品性质
    /// </summary>
    public string ProductNature { get; set; }

    /// <summary>
    /// 收容方式
    /// </summary>
    public string StorageMethod { get; set; }

    /// <summary>
    /// 收容数
    /// </summary>
    public decimal? StorageCount { get; set; }

    /// <summary>
    /// 单箱重量
    /// </summary>
    public decimal? BoxWeight { get; set; }

    /// <summary>
    /// 审核人
    /// </summary>
    public string Reviewer { get; set; }

    /// <summary>
    /// 审核日期
    /// </summary>
    public DateTime? ReviewDate { get; set; }

    /// <summary>
    /// 发布人
    /// </summary>
    public string Publisher { get; set; }

    /// <summary>
    /// 发布日期
    /// </summary>
    public DateTime? PublishDate { get; set; }

    /// <summary>
    /// 有效
    /// </summary>
    public bool? Valid { get; set; }

    /// <summary>
    /// 采购通知
    /// </summary>
    public bool? PurchaseNotice { get; set; }

    /// <summary>
    /// 生产通知
    /// </summary>
    public bool? ProductionNotice { get; set; }

    /// <summary>
    /// 储运通知
    /// </summary>
    public bool? StorageTransportNotice { get; set; }

    /// <summary>
    /// 取消标记
    /// </summary>
    public bool? CancelFlag { get; set; }

    /// <summary>
    /// 内外销
    /// </summary>
    public string DomesticExport { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? UnitPrice { get; set; }

    /// <summary>
    /// 单价更新人
    /// </summary>
    public string UnitPriceUpdater { get; set; }

    /// <summary>
    /// 单价更新日期
    /// </summary>
    public DateTime? UnitPriceUpdateDate { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 商品料号
    /// </summary>
    public string ProdMatCode { get; set; }

    /// <summary>
    /// 成品项号
    /// </summary>
    public string FGItemNo { get; set; }

    /// <summary>
    /// 商品编号
    /// </summary>
    public string ProductCode { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string SpecModel { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 产销国
    /// </summary>
    public string SalesCountry { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; }
}