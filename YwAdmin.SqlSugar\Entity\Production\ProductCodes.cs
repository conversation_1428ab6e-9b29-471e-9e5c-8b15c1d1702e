namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 商品编码表
/// </summary>
[SugarTable("Product_Codes")]
public partial class ProductCodes : BaseEntity
{
    /// <summary>
    /// 商品编号
    /// </summary>
    public string HSCode { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 商品描述
    /// </summary>
    public string ProductDec { get; set; }

    /// <summary>
    /// 所属大类
    /// </summary>
    public string Category { get; set; }

    /// <summary>
    /// 所属类别
    /// </summary>
    public string SubCategory { get; set; }

    /// <summary>
    /// 计量单位
    /// </summary>
    public string MeterUnit { get; set; }

    /// <summary>
    /// 交易单位
    /// </summary>
    public string TradUnit { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
}