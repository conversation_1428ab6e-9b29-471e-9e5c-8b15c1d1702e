﻿// Copyright © 2024-present wzw

using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.SqlSugar;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts;
using @NameSpace@.Dtos;
using @NameSpace@.Input;
using Mapster;
using Volo.Abp;

namespace @NameSpace@;

/// <summary>
/// @ClassName@服务
/// </summary>
/// <param name="db">数据</param>
/// <param name="currentUser"></param>
/// <param name="userRepository">用户表</param>
/// <param name="@className@Repository">@ClassName@表</param>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.PRODUCTION)]
public class @ClassName@Service(
    ISqlSugarClient db,
    ICurrentUser currentUser,
    Repository<UserEntity> userRepository,
    Repository<@ClassName@> @className@Repository
    ) : ProductionServiceBase
{
    private readonly Repository<@ClassName@> _@className@Repository = @className@Repository;
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<UserEntity> _userRepository = userRepository;

    /// <summary>
    /// 查询@ClassName@数据（分页）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> Get@ClassName@Page(@ClassName@Input input)
    {
        var query = _db.Queryable<@ClassName@>();

        // 应用条件查询
        // 如果需要添加条件，请在这里添加
        // if (!string.IsNullOrEmpty(input.SomeProperty))
        // {
        //     query = query.Where(x => x.SomeProperty.Contains(input.SomeProperty));
        // }

        // 应用分页
        var total = await query.CountAsync();
        var list = await query.OrderByDescending(x => x.CreateTime)
                             .Skip((input.PageIndex - 1) * input.PageSize)
                             .Take(input.PageSize)
                             .ToListAsync();

        var resultList = list.Select(
              result =>
              {
                  var dto = result.Adapt<@ClassName@Dto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  return dto;
              });

        return new TableData {
            Data = resultList,
            Total = total
        };
    }

    /// <summary>
    /// 查询所有@ClassName@数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> Get@ClassName@All(@ClassName@Input input)
    {
        var query = _db.Queryable<@ClassName@>();

        // 应用条件查询
        // 如果需要添加条件，请在这里添加
        // if (!string.IsNullOrEmpty(input.SomeProperty))
        // {
        //     query = query.Where(x => x.SomeProperty.Contains(input.SomeProperty));
        // }

        var list = await query.OrderByDescending(x => x.CreateTime).ToListAsync();
        var resultList = list.Select(
              result =>
              {
                  var dto = result.Adapt<@ClassName@Dto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  return dto;
              });

        return new TableData {
            Data = resultList
        };
    }

    /// <summary>
    /// 新增或者更新@ClassName@
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    ///
    [HttpPost]
    public async Task<TableData> AddOrEdit@ClassName@(@ClassName@Input input)
    {
        if (input.Id == null)
        {
            var entity = input.Adapt<@ClassName@>();
            await _@<EMAIL>(entity);
        }
        else
        {
            var @className@Info = await _db.Queryable<@ClassName@>().FirstAsync(x => x.Id == input.Id);
            if (@className@Info != null)
            {
                // Update properties here
                await _@<EMAIL>(@className@Info);
            }
            else
            {
                throw new UserFriendlyException(L["DataException"]);
            }
        }
        return new TableData { Data = L["SaveSuccessfule"] };
    }

    /// <summary>
    /// 查询@ClassName@明细数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> Get@ClassName@(@ClassName@Input input)
    {
        if (input.Id == null | input.Id == 0)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        var entity = await _db.Queryable<@ClassName@>().FirstAsync(x => x.Id == input.Id);
        var result = entity.Adapt<@ClassName@Dto>();

        result.CreateUserName = _userRepository.GetById(entity.CreateUserId)?.Name;
        result.UpdateUserName = _userRepository.GetById(entity.UpdateUserId)?.Name;
        return new TableData
        {
            Data = result,
        };
    }
}
