﻿// Copyright © 2024-present wzw

using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.Production.SubModules.BomServices.Dtos;

/// <summary>
/// 新增模型
/// </summary>
public class AddTCMDto : BaseDto
{
    public string MName { get; set; }

    public string MCode { get; set; }

    public string ProductCode { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string SpecModel { get; set; }

    /// <summary>
    /// 内部编号
    /// </summary>
    public string IntCode { get; set; }

    /// <summary>
    /// 料件项号
    /// </summary>
    public string MatItemNo { get; set; }

    /// <summary>
    /// 原产国
    /// </summary>
    public string OriginCountry { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? UnitPrice { get; set; }

    /// <summary>
    /// 门幅
    /// </summary>
    public decimal? Width { get; set; }

    /// <summary>
    /// 每平方米克重
    /// </summary>
    public decimal? GSM { get; set; } // Grams per Square Meter

    /// <summary>
    /// HS更新人
    /// </summary>
    public string HSUpdater { get; set; }

    /// <summary>
    /// HS更新时间
    /// </summary>
    public string HSTimeUpdated { get; set; }

    /// <summary>
    /// HS审核人
    /// </summary>
    public string Reviewer { get; set; }

    /// <summary>
    /// HS审核时间
    /// </summary>
    public DateTime? TimeReviewed { get; set; }

    /// <summary>
    /// 作废
    /// </summary>
    public bool? Obsolete { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 单品重量
    /// </summary>
    public decimal? SingleWeight { get; set; }

    /// <summary>
    /// 贸易方式
    /// </summary>
    public string TradeMode { get; set; }
}