﻿

using System.ComponentModel.DataAnnotations;

namespace YwAdmin.Multiplex.Contracts;
public class BaseDto
{
    /// <summary>
    /// Id
    ///</summary>
    public long? Id { get; set; }

    /// <summary>
    /// 创建人ID
    ///</summary>
    public long CreateUserId { get; set; }

    /// <summary>
    /// 创建时间
    ///</summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 修改ID
    ///</summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改时间
    ///</summary>
    public DateTime? UpdateTime { get; set; }

    public string? CreateUserName { get; set; }
    public string? UpdateUserName { get; set; }
}
