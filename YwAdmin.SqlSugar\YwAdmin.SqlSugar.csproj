﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoWarn>1701;1702;1591;8618;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Magicodes.IE.Core" Version="*******" />
    <PackageReference Include="Magicodes.IE.EPPlus" Version="*******" />
    <PackageReference Include="Serilog" Version="4.0.1" />
    <PackageReference Include="SqlSugarCore" Version="*********" />
    <PackageReference Include="Volo.Abp" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.Localization" Version="9.0.5" />
    <PackageReference Include="Volo.Abp.Timing" Version="8.2.1" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\YwAdmin.Multiplex.Contracts\YwAdmin.Multiplex.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="SqlSugar;" />
  </ItemGroup>

</Project>
