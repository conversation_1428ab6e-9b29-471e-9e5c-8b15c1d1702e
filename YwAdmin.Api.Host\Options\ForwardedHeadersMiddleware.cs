﻿﻿// Copyright © 2024-present wzw

using Microsoft.AspNetCore.HttpOverrides;

namespace YwAdmin.Api.Host.Options;

/// <summary>
/// 转发头中间件扩展
/// </summary>
public static class ForwardedHeadersMiddlewareExtensions
{
    /// <summary>
    /// 添加转发头中间件
    /// </summary>
    /// <param name="builder">应用程序构建器</param>
    /// <returns>应用程序构建器</returns>
    public static IApplicationBuilder UseCustomForwardedHeaders(this IApplicationBuilder builder)
    {
        // 配置转发头选项
        var options = new ForwardedHeadersOptions
        {
            ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedHost
        };
        
        // 允许所有网络作为代理
        options.KnownNetworks.Clear();
        options.KnownProxies.Clear();
        
        // 使用转发头中间件
        return builder.UseForwardedHeaders(options);
    }
}
