﻿
namespace YwAdmin.Multiplex.Contracts.helper;
public static class ConvertObj
{
    public static DateTime? ConvertExcelDateTimeIntoCLRDateTime(object value)
    {
        if (value == null) return null;
        if (value is DateTime)
        {
            return DateTime.Parse(value.ToString());
        }
        else if (DateTime.TryParse(value.ToString(), out var date))
        {
            return date;
        }
        else
        {
            return DateTime.FromOADate(Convert.ToInt32(value));
        }
    }
    public static string Obj2String(object obj)
    {
        try
        {
            var str = Convert.ToString(obj).Trim();
            return str;
        }
        catch
        {
            return "";
        }
    }
    public static DateTime? Obj2DateTime(object obj)
    {
        try
        {
            var str = Convert.ToDateTime(obj);
            return str;
        }
        catch
        {
            return null;
        }
    }

    public static int? Obj2Int(object obj)
    {
        try
        {
            return obj == null ? null :  Convert.ToInt32(obj);
        }
        catch (Exception)
        {
            return null; // 或者抛出异常，或者返回你认为适当的默认值
        }
    }
    public static decimal? Obj2Decimal(object obj)
    {
        try
        {
            return obj == null ? null : Convert.ToDecimal(obj);
        }
        catch (Exception)
        {
            return null; // 默认值为 0m，也可以根据需要修改为其他默认值
        }
    }


}
