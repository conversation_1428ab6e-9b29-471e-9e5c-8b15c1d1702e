﻿// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.SqlSugar;

namespace YwAdmin.SqlSugar.Entity.Basic;

/// <summary>
/// 公司信息表
/// </summary>
[SugarTable("Base_Warehouse")]
public partial class Warehouse : BaseEntity
{

    /// <summary>
    /// 仓库编号
    /// </summary>
    public string WarehouseCode { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    public string WarehouseName { get; set; }

    /// <summary>
    /// 仓库类别
    /// </summary>
    public string TypeCode { get; set; }

    /// <summary>
    /// 仓库地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 负责人
    /// </summary>
    public string ContactName { get; set; }

    /// <summary>
    /// 负责人联系电话
    /// </summary>
    public string ContactPhone { get; set; }

    /// <summary>
    /// 状态（1=启用，0=停用）
    /// </summary>
    public byte Status { get; set; }


}



