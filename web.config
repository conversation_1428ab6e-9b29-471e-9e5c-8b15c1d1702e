<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\YwAdmin.Api.Host.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
      <rewrite>
        <rules>
          <rule name="ReverseProxy" stopProcessing="true">
            <match url="^api/v1/(.*)" />
            <conditions>
              <!-- 确保只代理来自5777端口的请求 -->
              <add input="{HTTP_HOST}" pattern="localhost:5777" />
              <!-- 防止循环代理 -->
              <add input="{HTTP_X_FORWARDED_FOR}" pattern=".*" negate="true" />
            </conditions>
            <action type="Rewrite" url="http://localhost:5062/api/v1/{R:1}" />
            <serverVariables>
              <set name="HTTP_X_ORIGINAL_HOST" value="{HTTP_HOST}" />
              <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
            </serverVariables>
          </rule>
          <!-- 添加文件访问的重写规则 -->
          <rule name="FilesAccess" stopProcessing="true">
            <match url="^api/v1/files/(.*)" />
            <action type="Rewrite" url="http://localhost:5062/files/{R:1}" />
            <serverVariables>
              <set name="HTTP_X_ORIGINAL_HOST" value="{HTTP_HOST}" />
              <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
            </serverVariables>
          </rule>
        </rules>
      </rewrite>
      <!-- 添加MIME类型映射 -->
      <staticContent>
        <remove fileExtension=".jpg" />
        <mimeMap fileExtension=".jpg" mimeType="image/jpeg" />
        <remove fileExtension=".jpeg" />
        <mimeMap fileExtension=".jpeg" mimeType="image/jpeg" />
        <remove fileExtension=".png" />
        <mimeMap fileExtension=".png" mimeType="image/png" />
        <remove fileExtension=".gif" />
        <mimeMap fileExtension=".gif" mimeType="image/gif" />
      </staticContent>
    </system.webServer>
  </location>
</configuration>
<!--ProjectGuid: 038e492d-ec41-4b48-9494-9873c3ecdabe-->
