﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Localization\Purchase\en.json" />
    <None Remove="Localization\Purchase\vi.json" />
    <None Remove="Localization\Purchase\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\Purchase\en.json" />
    <EmbeddedResource Include="Localization\Purchase\vi.json" />
    <EmbeddedResource Include="Localization\Purchase\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\YwAdmin.Core\YwAdmin.Core.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex.Contracts\YwAdmin.Multiplex.Contracts.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex\YwAdmin.Multiplex.csproj" />
    <ProjectReference Include="..\..\YwAdmin.SqlSugar\YwAdmin.SqlSugar.csproj" />
  </ItemGroup>

</Project>
