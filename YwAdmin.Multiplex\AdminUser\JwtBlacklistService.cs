

using System.IdentityModel.Tokens.Jwt;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using YwAdmin.Multiplex.Contracts.IAdminUser;

namespace YwAdmin.Multiplex.AdminUser;

/// <summary>
/// JWT黑名单服务实现
/// </summary>
public class JwtBlacklistService : IJwtBlacklistService, ISingletonDependency
{
    private readonly IDistributedCache _cache;
    private readonly IConfiguration _configuration;
    private const string BlacklistPrefix = "JWT_BLACKLIST_";

    public JwtBlacklistService(IDistributedCache cache, IConfiguration configuration)
    {
        _cache = cache;
        _configuration = configuration;
    }

    /// <summary>
    /// 将Token添加到黑名单
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <param name="expiryMinutes">过期时间（分钟）</param>
    /// <returns></returns>
    public async Task AddToBlacklistAsync(string token, double expiryMinutes)
    {
        if (string.IsNullOrEmpty(token))
            return;

        // 从Token中提取JTI或其他唯一标识
        string tokenId = GetTokenIdentifier(token);
        string cacheKey = $"{BlacklistPrefix}{tokenId}";

        // 设置缓存选项
        var options = new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(expiryMinutes)
        };

        // 将Token添加到缓存中
        await _cache.SetStringAsync(cacheKey, token, options);
    }

    /// <summary>
    /// 检查Token是否在黑名单中
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>如果在黑名单中返回true，否则返回false</returns>
    public async Task<bool> IsInBlacklistAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        // 从Token中提取JTI或其他唯一标识
        string tokenId = GetTokenIdentifier(token);
        string cacheKey = $"{BlacklistPrefix}{tokenId}";

        // 检查缓存中是否存在该Token
        var cachedToken = await _cache.GetStringAsync(cacheKey);
        return !string.IsNullOrEmpty(cachedToken);
    }

    /// <summary>
    /// 从黑名单中移除Token
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns></returns>
    public async Task RemoveFromBlacklistAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return;

        // 从Token中提取JTI或其他唯一标识
        string tokenId = GetTokenIdentifier(token);
        string cacheKey = $"{BlacklistPrefix}{tokenId}";

        // 从缓存中移除Token
        await _cache.RemoveAsync(cacheKey);
    }

    /// <summary>
    /// 清理黑名单中过期的Token
    /// </summary>
    /// <returns></returns>
    public Task CleanupExpiredTokensAsync()
    {
        // 分布式缓存会自动清理过期的项，无需手动清理
        return Task.CompletedTask;
    }

    /// <summary>
    /// 从Token中提取唯一标识
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>Token的唯一标识</returns>
    private string GetTokenIdentifier(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token);

            // 优先使用JTI作为唯一标识
            var jti = jwtToken.Claims.FirstOrDefault(c => c.Type == "jti")?.Value;
            if (!string.IsNullOrEmpty(jti))
                return jti;

            // 如果没有JTI，使用用户ID和过期时间的组合作为唯一标识
            var userId = jwtToken.Claims.FirstOrDefault(c => c.Type == "userid")?.Value ?? "";
            var expiry = jwtToken.ValidTo.ToString("yyyyMMddHHmmss");
            return $"{userId}_{expiry}";
        }
        catch
        {
            // 如果解析失败，使用Token的哈希值作为唯一标识
            return token.GetHashCode().ToString();
        }
    }
}
