
namespace YwAdmin.Application.OrganizationServices.Dtos;
public class OrganizationOutput
{
	/// <summary>
	/// 主键Id
	/// </summary>
	public long Id { get; set; }
	/// <summary>
	/// 创建人
	/// </summary>
	public long Createuserid { get; set; }
	/// <summary>
	/// 创建时间
	/// </summary>
	public DateTime Createtime { get; set; }
	/// <summary>
	/// 修改人
	/// </summary>
	public long? Updatateuserid { get; set; }
	/// <summary>
	/// 修改时间
	/// </summary>
	public DateTime? Updatetime { get; set; }
	/// <summary>
	/// 备注
	/// </summary>
	public string Remark { get; set; }
	/// <summary>
	/// 名称
	/// </summary>
	public string Name { get; set; }
	/// <summary>
	/// 父级ID
	/// </summary>
	public long? ParentId { get; set; }
	/// <summary>
	/// 联系电话
	/// </summary>
	public string Telephone { get; set; }
	/// <summary>
	/// 负责人
	/// </summary>
	public string Leader { get; set; }
	/// <summary>
	/// 排序
	/// </summary>
	public string Sort { get; set; }
	public List<OrganizationOutput> Children { get; set; }
}
