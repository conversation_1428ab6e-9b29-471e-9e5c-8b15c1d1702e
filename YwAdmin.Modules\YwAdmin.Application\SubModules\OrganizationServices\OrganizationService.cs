
using YwAdmin.Application.OrganizationServices.Dtos;
using YwAdmin.SqlSugar;
using YwAdmin.Multiplex.Contracts.IAdminUser;

namespace YwAdmin.Application.OrganizationServices;

/// <summary>
/// Organization服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
public class OrganizationService(
    ISqlSugarClient db,
    ICurrentUser currentUser
    ) : ApplicationServiceBase
{
    private readonly ISqlSugarClient _db = db;

    /// <summary>
    /// 当前用户
    /// </summary>
    private readonly ICurrentUser _currentUser = currentUser;
    /// <summary>
    /// 组织机构扁平化数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<TableData> GetOrgList(GetPagedListInput input)
    {
        // 查询所有的组织数据
        var resultList = await _db.Queryable<OrganizationEntity>().OrderBy(x => x.Sort).ToListAsync();

        return new TableData
        {
            Data = resultList.Adapt<List<OrganizationOutput>>(),
        };
    }
    /// <summary>
    /// 组织机构的树形结构数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// 
    [HttpPost]
    public async Task<TableData> GetOrgTreeList(GetPagedListInput input)
    {
        // 查询所有的组织数据
        var resultList = await _db.Queryable<OrganizationEntity>().ToListAsync();

        // 构建树形结构
        var treeData = BuildTree(resultList);

        // 这里可以根据 input 的分页参数来进行分页（例如，通过跳过和取一定数量的元素）
        var pagedData = treeData.ToList();

        return new TableData
        {
            Data = pagedData.Adapt<List<OrganizationOutput>>(),
        };
    }

    // 构建树形结构的递归方法
    private List<OrganizationOutput> BuildTree(List<OrganizationEntity> entities)
    {
        // 获取所有的父节点（ParentId 为 null）
        var rootNodes = entities.Where(x => x.ParentId == null).ToList();

        // 递归构建树形结构
        var result = new List<OrganizationOutput>();
        foreach (var rootNode in rootNodes)
        {
            var rootOutput = new OrganizationOutput
            {
                Id = rootNode.Id,
                Name = rootNode.Name,
                ParentId = rootNode.ParentId,
                Leader = rootNode.Leader,
                Telephone = rootNode.Telephone,
                Children = GetChildren(rootNode.Id, entities) // 获取该节点的子节点
            };
            result.Add(rootOutput);
        }

        return result;
    }

    // 获取指定节点的所有子节点
    private List<OrganizationOutput> GetChildren(long parentId, List<OrganizationEntity> entities)
    {
        // 获取当前父节点的所有子节点
        var children = entities.Where(x => x.ParentId == parentId).ToList();
        var childOutputs = new List<OrganizationOutput>();

        foreach (var child in children)
        {
            var childOutput = new OrganizationOutput
            {
                Id = child.Id,
                Name = child.Name,
                ParentId = child.ParentId,
                Leader = child.Leader,
                Telephone = child.Telephone,
                Children = GetChildren(child.Id, entities) // 递归获取该子节点的子节点
            };
            childOutputs.Add(childOutput);
        }

        return childOutputs;
    }


    /// <summary>
    /// 单条查询
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<OrganizationOutput> GetAsync(long id)
    {
        var entity = await _db.Queryable<OrganizationEntity>().FirstAsync(x => x.Id == id);
        return entity.Adapt<OrganizationOutput>();
    }

    /// <summary>
    /// 添加组织机构
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<TableData> AddOrganization(AddOrganizationInput input)
    {
        if (string.IsNullOrEmpty(input.Name))
        {
            throw new UserFriendlyException(L["PleaseEnterName！"]);
        }
        input.Createuserid = _currentUser.Id;
        input.Createtime = DateTime.Now;
        var entity = input.Adapt<OrganizationEntity>();
        if (input.Id == null)
        {
            await _db.Insertable(entity).ExecuteReturnSnowflakeIdAsync();
        }
        else
        {
            await _db.Updateable(entity).ExecuteCommandAsync();
        }

        return new TableData
        {
            Message = L["UpdateSuccessful"]
        };
    }


    /// <summary>
    /// 删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// 
    [HttpPost]
    public async Task<TableData> DeleteOrganization(AddOrganizationInput input)
    {

        if (input.Id == null)
        {
            throw new UserFriendlyException(L["Abnormal！"]);
        }
        var entity = await _db.Queryable<OrganizationEntity>().FirstAsync(x => x.Id == input.Id);
        if (entity == null)
        {
            throw new UserFriendlyException(L["NotFound"]);
        }
        var orgChildren = await _db.Queryable<OrganizationEntity>().FirstAsync(x => x.ParentId == input.Id);
        if (orgChildren != null)
        {
            throw new UserFriendlyException(L["DeleteChild"]);
        }
        
        await _db.Deleteable(entity).ExecuteCommandAsync();
        return new TableData{ Message = L["UpdateSuccessful"] };
    }
}
