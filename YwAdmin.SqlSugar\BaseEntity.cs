﻿
namespace YwAdmin.SqlSugar;
public abstract class BaseEntity
{
    /// <summary>
    /// Id
    ///</summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 创建人ID
    ///</summary>
    [SugarColumn(ColumnName = "CREATEUSERID")]
    public long CreateUserId { get; set; }

    /// <summary>
    /// 创建时间
    ///</summary>
    [SugarColumn(ColumnName = "CREATETIME")]
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 修改ID
    ///</summary>
    [SugarColumn(ColumnName = "UPDATATEUSERID")]
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改时间
    ///</summary>
    [SugarColumn(ColumnName = "UPDATETIME")]
    public DateTime? UpdateTime { get; set; }


}
