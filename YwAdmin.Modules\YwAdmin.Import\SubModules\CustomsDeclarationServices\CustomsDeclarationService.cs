﻿// Copyright © 2024-present wzw

using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.SqlSugar.Entity.Import;
using YwAdmin.SqlSugar;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.Multiplex.Contracts;


namespace YwAdmin.Import.SubModules.CustomsDeclarationServices;

[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.IMPORT)]
public class CustomsDeclarationService(
    ISqlSugarClient db,
    Repository<ImportCustomsDeclaration> importCustomsDeclarationRepository
    ) : ImportBaseServiceBase
{
     private readonly ISqlSugarClient _db = db;
    private readonly Repository<ImportCustomsDeclaration> _importCustomsDeclarationRepository = importCustomsDeclarationRepository;


}
