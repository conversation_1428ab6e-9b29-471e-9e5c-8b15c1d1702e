

namespace YwAdmin.Multiplex.Contracts.IAdminUser;

/// <summary>
/// JWT黑名单服务接口
/// </summary>
public interface IJwtBlacklistService
{
    /// <summary>
    /// 将Token添加到黑名单
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <param name="expiryMinutes">过期时间（分钟）</param>
    /// <returns></returns>
    Task AddToBlacklistAsync(string token, double expiryMinutes);

    /// <summary>
    /// 检查Token是否在黑名单中
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>如果在黑名单中返回true，否则返回false</returns>
    Task<bool> IsInBlacklistAsync(string token);

    /// <summary>
    /// 从黑名单中移除Token
    /// </summary>
    /// <param name="token">JWT <PERSON></param>
    /// <returns></returns>
    Task RemoveFromBlacklistAsync(string token);

    /// <summary>
    /// 清理黑名单中过期的Token
    /// </summary>
    /// <returns></returns>
    Task CleanupExpiredTokensAsync();
}
