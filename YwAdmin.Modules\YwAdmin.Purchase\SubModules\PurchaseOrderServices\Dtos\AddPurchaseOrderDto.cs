// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;
using System.ComponentModel.DataAnnotations;

namespace YwAdmin.PurchaseOrder.PurchaseOrderServices.Dtos;

/// <summary>
/// 新增PurchaseOrder表DTO
/// </summary>
public class AddPurchaseOrderDto : BaseDto
{
    /// <summary>
    /// 订单号
    /// </summary>
    [Required(ErrorMessage = "订单号不能为空"), MaxLength(50, ErrorMessage = "订单号最大长度为：50")]
    public string Orderno { get; set; }

    /// <summary>
    /// 订单时间
    /// </summary>
    [Required(ErrorMessage = "订单时间不能为空")]
    public DateTime Orderdate { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    [Required(ErrorMessage = "订单类型不能为空"), MaxLength(20, ErrorMessage = "订单类型最大长度为：20")]
    public string Ordertype { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    [Required(ErrorMessage = "订单状态不能为空"), MaxLength(20, ErrorMessage = "订单状态最大长度为：20")]
    public string Orderstatus { get; set; }

    /// <summary>
    /// 采购订单号
    /// </summary>
    public string Pono { get; set; }

    /// <summary>
    /// 报关单号
    /// </summary>
    public string Declarationno { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    [Required(ErrorMessage = "供应商不能为空"), MaxLength(100, ErrorMessage = "供应商最大长度为：100")]
    public string Supplier { get; set; }

    /// <summary>
    /// 入库时间
    /// </summary>
    public DateTime? Receiptdate { get; set; }
}