﻿// Copyright © 2024-present wzw

using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.Production.SubModules.ProductServices.Dtos;
public class ProductInput
{
    public long? Id { get; set; }
    /// <summary>
	/// 商品编号
	/// </summary>
    public string HSCode { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 商品描述
    /// </summary>
    public string ProductDec { get; set; }

    /// <summary>
    /// 所属大类
    /// </summary>
    public string Category { get; set; }

    /// <summary>
    /// 所属类别
    /// </summary>
    public string SubCategory { get; set; }

    /// <summary>
    /// 计量单位
    /// </summary>
    public string MeterUnit { get; set; }

    /// <summary>
    /// 交易单位
    /// </summary>
    public string TradUnit { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
}
