﻿

using YwAdmin.Application.UserServices.Dtos;
using YwAdmin.Core.DataEncryption.Encryptions;

namespace YwAdmin.Application.UserServices.Mapper;
/// <summary>
/// 新增用户映射
/// </summary>
public class AddUserInputToEntity : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        _ = config.ForType<AddUserInput, UserEntity>()
            .IgnoreNullValues(true)
            .Map(dest => dest.Account, src => src.Account.ToLower())
            .Map(dest => dest.Password, src => MD5Encryption.Encrypt(src.Password, false, false));
    }
}
