﻿

using Microsoft.Extensions.DependencyInjection;

using YwAdmin.SqlSugar.Entity;

using Volo.Abp;
using Volo.Abp.Modularity;

namespace YwAdmin.SqlSugar
{
    public class AdminSqlSugarModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            context.Services.AddSqlSugarService();
            context.Services.ReplaceSqlSugarSnowflakeIdService();
        }
        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            var _db = context.ServiceProvider.GetRequiredService<ISqlSugarClient>();
            //保证只配置一次不能更新,该配置是全局静态存储
            if (!_db.ConfigQuery.Any())
            {
                //字典

                ////组织机构
                ///
            }
        }
    }
}
