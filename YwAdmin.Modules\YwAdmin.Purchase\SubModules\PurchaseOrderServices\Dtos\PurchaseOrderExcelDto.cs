// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.Purchase.PurchaseOrderDetailServices.Dtos;

namespace YwAdmin.PurchaseOrder.PurchaseOrderServices.Dtos;

/// <summary>
/// PurchaseOrder表DTO
/// </summary>
public class PurchaseOrderExcelDto : BaseDto
{
    public PurchaseOrderDto PurchaseOrder { get; set; }
    public string OrderType { get; set; }

    public List<PurchaseOrderDetailExcelDto> PurchaseOrderDetail { get; set; }
}

public class PurchaseOrderDetailExcelDto : PurchaseOrderDetailDto
{
    /// <summary>
    /// 导入错误信息
    /// </summary>
    public string ErrorMsg { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public bool? HasErrorMsg { get; set; }
}