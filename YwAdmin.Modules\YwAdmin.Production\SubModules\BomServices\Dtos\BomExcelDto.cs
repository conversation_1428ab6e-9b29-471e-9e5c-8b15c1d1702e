﻿
namespace YwAdmin.Production.SubModules.BomServices.Dtos;
/// <summary>
/// Bom导入格式处理
/// </summary>
public class BomExcelDto
{
    /// <summary>
    /// 成品：商品料号
    /// </summary>
    public string? ProdMatCode { get; set; }
    /// <summary>
    /// 成品：系列号
    /// </summary>
    public string? SeriesNo { get; set; }
    /// <summary>
    /// 成品：系列号
    /// </summary>
    public string? StyleNo { get; set; }
    /// <summary>
    /// 原材料：内部编号
    /// </summary>
    public string? IntCode { get; set; }
    /// <summary>
    /// 原材料名称
    /// </summary>
    public string? MName { get; set; }
    /// <summary>
    /// 商品料号(原材料)
    /// </summary>
    public string? MCode { get; set; }
        /// <summary>
    /// 商品料号(成品)
    /// </summary>
    public string? ItemMCode { get; set; }
    /// <summary>
    /// 单耗：取套
    /// </summary>
    public decimal? Casing { get; set; }
    /// <summary>
    /// 原始单耗
    /// </summary>
    public decimal? OriginalUnit { get; set; }

    public decimal? UnitConsumption{ get; set; }
    /// <summary>
    /// 有效使用率
    /// </summary>
    public decimal? EffectiveUsageRate { get; set; }
    /// <summary>
    /// 拖布长度
    /// </summary>
    public decimal? MopLength { get; set; }

    /// <summary>
    /// 导入错误信息
    /// </summary>
    public string ErrorMsg{ get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
     public bool? HasErrorMsg{ get; set; }
}
