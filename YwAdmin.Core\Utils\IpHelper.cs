﻿﻿// Copyright © 2024-present wzw

using Microsoft.AspNetCore.Http;
using System.Net;

namespace YwAdmin.Core.Utils;

/// <summary>
/// IP地址帮助类
/// </summary>
public static class IpHelper
{
    /// <summary>
    /// 获取客户端真实IP地址
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>IP地址</returns>
    public static string GetClientIp(HttpContext context)
    {
        // 优先从X-Real-IP获取
        var ip = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ip))
            return ip;

        // 其次从X-Forwarded-For获取
        ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ip))
        {
            // X-Forwarded-For可能包含多个IP，取第一个非内网IP
            var ips = ip.Split(',', StringSplitOptions.RemoveEmptyEntries);
            foreach (var address in ips)
            {
                var trimmedIp = address.Trim();
                if (!IsPrivateIpAddress(trimmedIp))
                    return trimmedIp;
            }
            // 如果都是内网IP，取第一个
            return ips[0].Trim();
        }

        // 最后从连接信息获取
        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }

    /// <summary>
    /// 判断是否为内网IP
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否为内网IP</returns>
    private static bool IsPrivateIpAddress(string ipAddress)
    {
        if (string.IsNullOrEmpty(ipAddress) || ipAddress.Equals("localhost", StringComparison.OrdinalIgnoreCase))
            return true;

        if (!IPAddress.TryParse(ipAddress, out var ip))
            return false;

        // 检查是否为内网IP
        byte[] bytes = ip.GetAddressBytes();
        return ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork &&
               (
                   // 10.0.0.0/8
                   bytes[0] == 10 ||
                   // **********/12
                   (bytes[0] == 172 && bytes[1] >= 16 && bytes[1] <= 31) ||
                   // ***********/16
                   (bytes[0] == 192 && bytes[1] == 168) ||
                   // *********/8
                   bytes[0] == 127
               );
    }

    /// <summary>
    /// 获取设备信息哈希
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>设备信息哈希</returns>
    public static string GetDeviceInfoHash(HttpContext context)
    {
        var userAgent = context.Request.Headers["User-Agent"].ToString();
        var ipAddress = GetClientIp(context);

        // 简单的设备信息哈希
        return $"{userAgent}|{ipAddress}".GetHashCode().ToString();
    }
}
