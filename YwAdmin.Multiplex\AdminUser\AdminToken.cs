﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

using YwAdmin.Multiplex.Contracts.IAdminUser;

using Volo.Abp.Timing;

namespace YwAdmin.Multiplex.AdminUser;

public class AdminToken : IAdminToken, ISingletonDependency
{
    private readonly IConfiguration _configuration;
    private readonly IClock _clock;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IJwtBlacklistService _jwtBlacklistService;

    public AdminToken(
        IConfiguration configuration,
        IClock clock,
        IHttpContextAccessor httpContextAccessor,
        IJwtBlacklistService jwtBlacklistService)
    {
        _configuration = configuration;
        _clock = clock;
        _httpContextAccessor = httpContextAccessor;
        _jwtBlacklistService = jwtBlacklistService;
    }

    /// <summary>
    /// 生成token字符串
    /// </summary>
    /// <param name="claims">用户声明</param>
    /// <returns>JWT Token字符串</returns>
    public string GenerateTokenString(IEnumerable<Claim> claims)
    {
        // 获取JWT配置选项
        var jwtOptions = _configuration.GetRequiredSection("JwtOptions").Get<JwtOptions>() ?? new JwtOptions();

        // 创建声明列表的可变副本
        var claimsList = claims.ToList();

        // 如果启用了JTI，添加JWT ID声明
        if (jwtOptions.EnableJti)
        {
            var jti = GenerateJti();
            claimsList.Add(new Claim(JwtRegisteredClaimNames.Jti, jti));
        }

        // 如果启用了设备信息验证，添加设备信息声明
        if (jwtOptions.ValidateDeviceInfo)
        {
            var deviceInfo = GetDeviceInfo();
            if (!string.IsNullOrEmpty(deviceInfo))
            {
                claimsList.Add(new Claim("device_info", deviceInfo));
            }
        }

        // 取出私钥并以utf8编码字节输出
        var key = Encoding.UTF8.GetBytes(jwtOptions.SecretKey);
        // 使用对称算法对私钥进行加密
        var signingKey = new SymmetricSecurityKey(key);
        // 选择签名算法
        var signingAlgorithm = SecurityAlgorithms.HmacSha256;
        // 使用HmacSha256来验证加密后的私钥生成数字签名
        var signingCredentials = new SigningCredentials(signingKey, signingAlgorithm);

        // 创建JWT安全令牌
        var token = new JwtSecurityToken(
            issuer: jwtOptions.Issuer,
            audience: jwtOptions.Audience,
            claims: claimsList,
            notBefore: _clock.Now,
            expires: _clock.Now.AddMinutes(jwtOptions.ExpiredMinutes),
            signingCredentials: signingCredentials
        );

        // 生成字符串token
        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    /// <summary>
    /// 生成JWT ID
    /// </summary>
    /// <returns>唯一的JWT ID</returns>
    private string GenerateJti()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <returns>设备信息字符串</returns>
    private string GetDeviceInfo()
    {
        if (_httpContextAccessor.HttpContext == null)
            return string.Empty;

        // 使用IpHelper获取真实客户端IP
        return YwAdmin.Core.Utils.IpHelper.GetDeviceInfoHash(_httpContextAccessor.HttpContext);
    }

    /// <summary>
    /// 返回刷新分钟数
    /// </summary>
    /// <returns>刷新分钟数</returns>
    public double GetRefreshMinutes()
    {
        var jwtOptions = _configuration.GetRequiredSection("JwtOptions").Get<JwtOptions>() ?? new JwtOptions();
        return jwtOptions.RefreshMinutes;
    }

    /// <summary>
    /// 验证Token
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>验证结果，成功返回true，失败返回false</returns>
    public async Task<bool> ValidateTokenAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        var jwtOptions = _configuration.GetRequiredSection("JwtOptions").Get<JwtOptions>() ?? new JwtOptions();

        try
        {
            // 检查Token是否在黑名单中
            if (jwtOptions.EnableBlacklist && await _jwtBlacklistService.IsInBlacklistAsync(token))
                return false;

            // 解析Token
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token);

            // 检查Token是否过期
            if (jwtToken.ValidTo < _clock.Now.ToUniversalTime())
                return false;

            // 如果启用了设备信息验证，验证设备信息
            if (jwtOptions.ValidateDeviceInfo)
            {
                var tokenDeviceInfo = jwtToken.Claims.FirstOrDefault(c => c.Type == "device_info")?.Value;
                var currentDeviceInfo = GetDeviceInfo();

                // 添加日志，帮助调试设备信息验证
                //_logger.LogInformation($"设备信息验证: Token中的设备信息={tokenDeviceInfo}, 当前设备信息={currentDeviceInfo}");

                if (string.IsNullOrEmpty(tokenDeviceInfo))
                {
                    //_logger.LogWarning("设备信息验证失败: Token中没有设备信息");
                    return false;
                }

                if (tokenDeviceInfo != currentDeviceInfo)
                {
                    //_logger.LogWarning("设备信息验证失败: 设备信息不匹配");
                    return false;
                }
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 使Token失效（加入黑名单）
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>操作结果</returns>
    public async Task<bool> RevokeTokenAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        var jwtOptions = _configuration.GetRequiredSection("JwtOptions").Get<JwtOptions>() ?? new JwtOptions();

        if (!jwtOptions.EnableBlacklist)
            return false;

        try
        {
            // 解析Token获取过期时间
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token);

            // 计算Token剩余有效时间（分钟）
            var expiryTime = jwtToken.ValidTo;
            var remainingMinutes = (expiryTime - _clock.Now.ToUniversalTime()).TotalMinutes;

            // 如果Token已过期，不需要加入黑名单
            if (remainingMinutes <= 0)
                return true;

            // 将Token加入黑名单，过期时间设置为Token的剩余有效期或配置的黑名单过期时间，取较小值
            var blacklistExpiryMinutes = Math.Min(remainingMinutes, jwtOptions.BlacklistExpiryMinutes);
            await _jwtBlacklistService.AddToBlacklistAsync(token, blacklistExpiryMinutes);

            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// JWT配置选项
/// </summary>
public class JwtOptions
{
    /// <summary>
    /// 密钥
    /// </summary>
    public string SecretKey { get; set; } = "http://www.YwAdmin.com";

    /// <summary>
    /// 过期分钟
    /// </summary>
    public double ExpiredMinutes { get; set; } = 60;

    /// <summary>
    /// 刷新分钟数
    /// </summary>
    public double RefreshMinutes { get; set; } = 30;

    /// <summary>
    /// 发行者
    /// </summary>
    public string Issuer { get; set; } = "YwAdmin";

    /// <summary>
    /// 接收者
    /// </summary>
    public string Audience { get; set; } = "YwAdmin.Client";

    /// <summary>
    /// 是否启用黑名单
    /// </summary>
    public bool EnableBlacklist { get; set; } = true;

    /// <summary>
    /// 黑名单缓存过期时间（分钟）
    /// </summary>
    public double BlacklistExpiryMinutes { get; set; } = 1440; // 默认24小时

    /// <summary>
    /// 是否验证设备信息
    /// </summary>
    public bool ValidateDeviceInfo { get; set; } = true;

    /// <summary>
    /// 是否启用JTI（JWT ID）
    /// </summary>
    public bool EnableJti { get; set; } = true;

    /// <summary>
    /// 是否启用自动登出（长时间不活跃）
    /// </summary>
    public bool EnableAutoLogout { get; set; } = true;

    /// <summary>
    /// 自动登出时间（分钟）
    /// </summary>
    public double AutoLogoutMinutes { get; set; } = 30;
}