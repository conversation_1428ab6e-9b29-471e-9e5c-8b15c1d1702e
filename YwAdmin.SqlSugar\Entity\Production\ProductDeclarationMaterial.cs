namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 申报要素表
/// </summary>
[SugarTable("Product_Declaration_Material")]
public partial class ProductDeclarationMaterial : BaseEntity
{
    /// <summary>
    /// 商品编号
    /// </summary>
    public string HSCode { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int? Seq { get; set; }

    /// <summary>
    /// 商品申报要素
    /// </summary>
    public string Declaration { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 要素分类
    /// </summary>
    //public string Category { get; set; }

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool? IsMust { get; set; }

    /// <summary>
    /// 要素编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 编码内容
    /// </summary>
    public string CodeContent { get; set; }

    /// <summary>
    /// 料号编码
    /// </summary>
    public string MCode { get; set; }
}