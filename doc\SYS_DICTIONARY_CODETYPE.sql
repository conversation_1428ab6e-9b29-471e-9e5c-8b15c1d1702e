
CREATE TABLE [dbo].[SYS_DICTIONARY_CODETYPE] (
   [Id] int  NOT NULL IDENTITY(1,1),
  [CodeType] varchar(40) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [CodeTypeName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Description] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [Seq] varchar(10) COLLATE Chinese_PRC_CI_AS  NULL,
  [LastModificationTime] datetime DEFAULT getdate() NULL,
  [LastModifierUserId] bigint  NULL,
  [CreationTime] datetime  NOT NULL,
  [CreatorUserId] bigint  NULL,
CONSTRAINT [PK_dbo.SYS_DICTIONARY_CODETYPE] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SYS_DICTIONARY_CODETYPE] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'代码类型',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODETYPE',
'COLUMN', N'CodeType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'代码类型名称',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODETYPE',
'COLUMN', N'CodeTypeName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'描述',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODETYPE',
'COLUMN', N'Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'序列',
'SCHEMA', N'dbo',
'TABLE', N'SYS_DICTIONARY_CODETYPE',
'COLUMN', N'Seq'
GO


-- ----------------------------

