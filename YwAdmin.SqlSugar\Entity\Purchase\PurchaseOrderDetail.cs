namespace YwAdmin.SqlSugar.Entity;

/// <summary>
///
/// </summary>
[SugarTable("PURCHASE_ORDER_DETAIL")]
public partial class PurchaseOrderDetail : BaseEntity
{
    /// <summary>
    /// 订单号
    /// </summary>
    public string OrderNo { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int Seq { get; set; }

    /// <summary>
    /// 原材料料号
    /// </summary>
    public string MCode { get; set; }

    /// <summary>
    /// 商品编号
    /// </summary>
    public string ItemCode { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string ItemName { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string SpecModel { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal Qty { get; set; }

    /// <summary>
    /// 件数
    /// </summary>
    public int? PackageCount { get; set; }

    /// <summary>
    /// 毛重
    /// </summary>
    public decimal? GrossWeight { get; set; }

    /// <summary>
    /// 净重
    /// </summary>
    public decimal? NetWeight { get; set; }

    /// <summary>
    /// 体积
    /// </summary>
    public decimal? Volume { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? UnitPrice { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }
}