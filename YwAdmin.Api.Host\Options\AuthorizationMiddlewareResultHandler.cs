﻿

using System.Linq;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Http;

namespace YwAdmin.Api.Host.Options;
public class AuthorizationMiddlewareResultHandler : IAuthorizationMiddlewareResultHandler
{
    public async Task HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
    {
        // 如果状态码已经被设置为401，直接返回，不再执行后续逻辑
        if (context.Response.StatusCode == StatusCodes.Status401Unauthorized)
        {
            return;
        }

        if (authorizeResult.Challenged)
        {
            await context.ChallengeAsync();
            return;
        }
        else if (authorizeResult.Forbidden)
        {
            // 检查是否有认证相关的失败原因
            var authReasons = authorizeResult.AuthorizationFailure?.FailureReasons;
            if (authReasons != null && authReasons.Any(r => r.Message.Contains("登录") || r.Message.Contains("Token")))
            {
                // 如果失败原因与认证相关，返回401而不是403
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                return;
            }

            await context.ForbidAsync();
            return;
        }
        await next(context);
    }
}
