using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Polly;
using System.Security.Cryptography;
using YwAdmin.Application.CurrentUserContext;
using YwAdmin.Core.DataEncryption.Encryptions;
using YwAdmin.Multiplex.AdminUser;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Application.UserServices.Dtos;
using YwAdmin.SqlSugar.Entity;
using SqlSugar.Extensions;
using System.DirectoryServices.ActiveDirectory;
using System.Security.Principal;
using YamlDotNet.RepresentationModel;
using YwAdmin.Application.Dtos.Input;
using YwAdmin.SqlSugar.Entity.Basic;
using YwAdmin.Application.CompanyServices.Dtos;

namespace YwAdmin.Application.CompanyService
{
    /// <summary>
    /// 公司信息
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
    public class CompanyService(
        ISqlSugarClient db,
        Repository<UserEntity> userRepository,
        Repository<Role> roleRepository,
        ICurrentUserContext context,
        Repository<RoleFunction> roleFunctionRepository,
        Repository<UserRoleEntity> userRoleRepository
        ) : ApplicationServiceBase
    {
        private readonly ISqlSugarClient _db = db;
        private readonly Repository<Role> _roleRepository = roleRepository;
        private readonly Repository<RoleFunction> _roleFunctionRepository = roleFunctionRepository;
        private readonly ICurrentUserContext _context = context;
        private readonly Repository<UserRoleEntity> _userRoleRepository = userRoleRepository;
        private readonly Repository<UserEntity> _userRepository = userRepository;

        /// <summary>
        /// 获取对应的公司信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task<TableData> GetCompanyList(CompanyInput input)
        {
            var query = _db.Queryable<CompanyInfo>();

            var resultList = await query.ToListAsync();

            return new TableData
            {
                Data = resultList.Adapt<List<CompanyDto>>()
            };

        }

    }
};