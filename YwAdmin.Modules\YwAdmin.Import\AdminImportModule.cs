﻿using System.Reflection;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.Validation.Localization;
using Volo.Abp.VirtualFileSystem;
using YwAdmin.Multiplex;
using YwAdmin.Import.Localization;
using YwAdmin.Core.Mapster;

namespace YwAdmin.Production
{
    [DependsOn(typeof(AdminMultiplexModule), typeof(AbpLocalizationModule))]
    public class AdminImportModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.AddEmbedded<AdminImportModule>("YwAdmin.Import");
            });
            // 注册 Mapster
            context.Services.AddMapsterIRegister(Assembly.GetExecutingAssembly());
            Configure<AbpAspNetCoreMvcOptions>(options =>
            {
                options.ConventionalControllers.Create(typeof(AdminImportModule).Assembly, opts =>
                {
                    opts.RootPath = "v1";
                    opts.UrlActionNameNormalizer = (action) =>
                    {
                        // 确保动作名称不被修改
                        return action.ActionNameInUrl;
                    };
                });
            });
            Configure<AbpLocalizationOptions>(options =>
            {
                options.DefaultResourceType = typeof(ImportResource);
                options.Resources
                  .Add<ImportResource>("en") //Define the resource by "en" default culture
                  .AddBaseTypes(typeof(AbpValidationResource)) //Inherit from an existing resource
                  .AddVirtualJson("/Localization/Production"); //Add strings from virtual json files
                options.Languages.Add(new LanguageInfo("en", "English", "famfamfam-flag-england"));
                options.Languages.Add(new LanguageInfo("zh-Hans", "简体中文", "famfamfam-flag-cn")); // 简体中文
                options.Languages.Add(new LanguageInfo("vi", "Tiếng Việt", "famfamfam-flag-vn")); // 越南文
            });
        }
    }
}