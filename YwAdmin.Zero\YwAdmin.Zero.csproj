﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Templates\ServiceTemplate.txt" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Templates\AddTemplate.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\ServiceTemplate.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\OutputTemplate.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\DtoTemplate.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\InputTemplate.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Castle.Core" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.Localization" Version="9.0.5" />
    <PackageReference Include="Volo.Abp.Timing" Version="8.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\YwAdmin.Core\YwAdmin.Core.csproj" />
    <ProjectReference Include="..\YwAdmin.SqlSugar\YwAdmin.SqlSugar.csproj" />
    <ProjectReference Include="..\YwAdmin.Core\YwAdmin.Core.csproj" />
    <ProjectReference Include="..\YwAdmin.SqlSugar\YwAdmin.SqlSugar.csproj" />
  </ItemGroup>

</Project>
