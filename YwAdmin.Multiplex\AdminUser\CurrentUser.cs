﻿

using YwAdmin.Core.Cache;
using YwAdmin.Core.ExceptionExtensions;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.Multiplex.Contracts.IAdminUser;

namespace YwAdmin.Multiplex.AdminUser;
/// <summary>
/// 当前用户
/// </summary>
public class CurrentUser(ISqlSugarClient db, IHttpContextAccessor httpContextAccessor, IAdminCache cache) : ICurrentUser, ITransientDependency
{
    /// <summary>
    /// db
    /// </summary>
    private readonly ISqlSugarClient _db = db;

    /// <summary>
    /// HttpContext 访问器
    /// </summary>
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    /// <summary>
    /// 缓存
    /// </summary>
    private readonly IAdminCache _cache = cache;

    /// <summary>
    /// 用户Id
    /// </summary>
    public long Id
    {
        get => long.Parse(_httpContextAccessor.HttpContext?.User.FindFirst(AdminClaimConst.USER_ID)?.Value ?? throw PersistdValidateException.Message("令牌超时，请重新登录！"));
    }

    public long GetUserId()
    {
        // 获取 "userid" Claim 的值并转换为 long 类型
        var userIdString = _httpContextAccessor?.HttpContext?.User?.Claims
            .FirstOrDefault(c => c.Type == "userid")?.Value;

        if (long.TryParse(userIdString, out var userId))
        {
            return userId;
        }
        return 1;
    }

    /// <summary>
    /// 组织机构Id
    /// </summary>
    public long OrganizationId
    {
        get => long.Parse(_httpContextAccessor.HttpContext?.User.FindFirst(AdminClaimConst.ORGANIZATION_ID)?.Value ?? throw PersistdValidateException.Message("令牌超时，请重新登录！"));
    }
    public string GetUsername()
    {
        return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(c => c.Type == "name").Value;
    }
    public string GetEmail()
    {
        return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(c => c.Type == "email")?.Value;
    }
    public string GetAccount()
    {
        return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(c => c.Type == "account").Value;
    }

    public string GetRoles()
    {
        //return _httpContextAccessor?.HttpContext?.User?.Claims
        //    .Where(c => c.Type == "role" || c.Type == ClaimTypes.Role)
        //    .Select(c => c.Value) ?? Enumerable.Empty<string>();
        return "";
    }
    public UserContext GetUserInfo()
    {
        var currentUser = new UserContext();
        currentUser.UserId = GetUserId();
        currentUser.Name = GetUsername();
        currentUser.Account = GetAccount();
        currentUser.Email = GetEmail();

        return currentUser;
    }

}
