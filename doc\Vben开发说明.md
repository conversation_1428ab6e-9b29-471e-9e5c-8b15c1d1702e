# web-ele前端开发说明记录

## 一、新增路由

​	文件路径：

src/router/routes/modules里就可添加



语言文件

locales

1、ele 添加文本框

```
<ElCard class="mb-5 w-auto">
    <ElSpace>
      <!-- 添加用户管理文本框 -->
       <ElForm>
        <ElFormItem>
          <label for="userName">
            用户名称
          </label>
          <ElInput id="userName" name="userName" v-model="data" ></ElInput>
          <label for="data2">
            用户名称
          </label>
          <ElInput id="data2" name="data2" v-model="data2" ></ElInput>
        </ElFormItem>
       </ElForm>
    </ElSpace>
  </ElCard>
```

1、用户管理

主页面：

列表显示：

| 用户名 | 真实姓名 | 锁定 | 备注 | 创建时间 | 操作 |
| ------ | -------- | ---- | ---- | -------- | ---- |
|        |          |      |      |          |      |
|        |          |      |      |          |      |
|        |          |      |      |          |      |

