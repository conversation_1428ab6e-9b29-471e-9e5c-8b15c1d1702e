using Microsoft.AspNetCore.Authorization;
using YwAdmin.Application.TokenServices.Dtos;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts.Models;
using Microsoft.AspNetCore.Http;

namespace YwAdmin.Application.TokenServices;

/// <summary>
/// Token管理服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
public class TokenManagementService : ApplicationService
{
    private readonly IDbTokenService _dbTokenService;
    private readonly ISqlSugarClient _db;
    private readonly ICurrentUser _currentUser;
    private readonly IHttpContextAccessor _httpContextAccessor ;
    public TokenManagementService(
        IDbTokenService dbTokenService,
        ISqlSugarClient db,
        IHttpContextAccessor httpContextAccessor,
        ICurrentUser currentUser)
    {
        _dbTokenService = dbTokenService;
        _db = db;
        _currentUser = currentUser;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// 获取当前用户的所有活跃Token
    /// </summary>
    /// <returns>Token列表</returns>
    [HttpGet]
    public async Task<TableData> GetMyActiveTokens()
    {
        try
        {
            var userId = _currentUser.Id;
            var tokens = await _dbTokenService.GetUserActiveTokensAsync(userId);

            var result = tokens.Select(t => new TokenOutput
            {
                Id = t.Id,
                DeviceInfo = t.DeviceInfo,
                IpAddress = t.IpAddress,
                UserAgent = t.UserAgent,
                LastActivityTime = t.LastActivityTime,
                ExpireTime = t.ExpireTime
            }).ToList();

            return new TableData
            {
                Code = 0,
                Message = "获取成功",
                Data = result
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 500,
                Message = exc.Message
            };
        }
    }

    /// <summary>
    /// 撤销指定Token
    /// </summary>
    /// <param name="id">Token ID</param>
    /// <returns>操作结果</returns>
    [HttpPost]
    public async Task<TableData> RevokeToken(long id)
    {
        try
        {
            var userId = _currentUser.Id;

            // 获取Token信息
            var token = await _db.Queryable<UserTokenEntity>()
                .FirstAsync(t => t.Id == id);

            if (token == null)
            {
                return new TableData
                {
                    Code = 404,
                    Message = "Token不存在"
                };
            }

            // 验证Token是否属于当前用户
            if (token.UserId != userId)
            {
                return new TableData
                {
                    Code = 403,
                    Message = "无权操作此Token"
                };
            }

            // 撤销Token
            await _dbTokenService.RevokeTokenAsync(token.TokenValue);

            return new TableData
            {
                Code = 0,
                Message = "撤销成功"
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 500,
                Message = exc.Message
            };
        }
    }

    /// <summary>
    /// 撤销当前用户的所有Token（除当前Token外）
    /// </summary>
    /// <returns>操作结果</returns>
    [HttpPost]
    public async Task<TableData> RevokeAllMyTokens()
    {
        try
        {
            var userId = _currentUser.Id;

            // 获取当前Token
            var currentToken = _httpContextAccessor.HttpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

            // 获取用户所有活跃Token
            var tokens = await _dbTokenService.GetUserActiveTokensAsync(userId);

            // 撤销除当前Token外的所有Token
            foreach (var token in tokens)
            {
                if (token.TokenValue != currentToken)
                {
                    await _dbTokenService.RevokeTokenAsync(token.TokenValue);
                }
            }

            return new TableData
            {
                Code = 0,
                Message = "撤销成功"
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 500,
                Message = exc.Message
            };
        }
    }

    /// <summary>
    /// 管理员获取所有活跃Token
    /// </summary>
    /// <returns>Token列表</returns>
    [HttpGet]
    [Authorize(Roles = "Admin")]
    public async Task<TableData> GetAllActiveTokens()
    {
        try
        {
            // 获取所有活跃Token
            var tokens = await _db.Queryable<UserTokenEntity>()
                .Where(t => t.IsActive && t.ExpireTime > DateTime.Now)
                .ToListAsync();

            // 获取用户信息
            var userIds = tokens.Select(t => t.UserId).Distinct().ToList();
            var users = await _db.Queryable<UserEntity>()
                .Where(u => userIds.Contains(u.Id))
                .ToListAsync();

            var result = tokens.Select(t => new AdminTokenOutput
            {
                Id = t.Id,
                UserId = t.UserId,
                UserName = users.FirstOrDefault(u => u.Id == t.UserId)?.Name ?? "未知用户",
                DeviceInfo = t.DeviceInfo,
                IpAddress = t.IpAddress,
                UserAgent = t.UserAgent,
                LastActivityTime = t.LastActivityTime,
                ExpireTime = t.ExpireTime
            }).ToList();

            return new TableData
            {
                Code = 0,
                Message = "获取成功",
                Data = result
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 500,
                Message = exc.Message
            };
        }
    }

    /// <summary>
    /// 管理员撤销指定用户的所有Token
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<TableData> RevokeUserTokens(long userId)
    {
        try
        {
            // 撤销用户所有Token
            await _dbTokenService.RevokeAllUserTokensAsync(userId);

            return new TableData
            {
                Code = 0,
                Message = "撤销成功"
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 500,
                Message = exc.Message
            };
        }
    }

    /// <summary>
    /// 清理过期Token
    /// </summary>
    /// <returns>操作结果</returns>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<TableData> CleanupExpiredTokens()
    {
        try
        {
            // 清理过期Token
            var count = await _dbTokenService.CleanupExpiredTokensAsync();

            return new TableData
            {
                Code = 0,
                Message = $"清理成功，共清理{count}个过期Token"
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 500,
                Message = exc.Message
            };
        }
    }
}
