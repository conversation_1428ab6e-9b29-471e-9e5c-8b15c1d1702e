// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.PurchaseOrder.PurchaseOrderServices.Dtos;

/// <summary>
/// PurchaseOrder表输出模型
/// </summary>
public class PurchaseOrderOutput : BaseDto
{
	/// <summary>
	/// 
	/// </summary>
	public long Id { get; set; }
	/// <summary>
	/// 订单号
	/// </summary>
	public string Orderno { get; set; }
	/// <summary>
	/// 订单时间
	/// </summary>
	public DateTime Orderdate { get; set; }
	/// <summary>
	/// 订单类型
	/// </summary>
	public string Ordertype { get; set; }
	/// <summary>
	/// 订单状态
	/// </summary>
	public string Orderstatus { get; set; }
	/// <summary>
	/// 采购订单号
	/// </summary>
	public string Pono { get; set; }
	/// <summary>
	/// 报关单号
	/// </summary>
	public string Declarationno { get; set; }
	/// <summary>
	/// 供应商
	/// </summary>
	public string Supplier { get; set; }
	/// <summary>
	/// 入库时间
	/// </summary>
	public DateTime? Receiptdate { get; set; }
}
