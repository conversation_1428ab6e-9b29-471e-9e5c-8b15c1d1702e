

using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using SqlSugar;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts.Models;
using Volo.Abp.Timing;
using Volo.Abp.Users;

namespace YwAdmin.Multiplex.AdminUser;

/// <summary>
/// 数据库Token管理服务实现
/// </summary>
public class DbTokenService : IDbTokenService, ISingletonDependency
{
    private readonly ISqlSugarClient _db;
    private readonly IConfiguration _configuration;
    private readonly IClock _clock;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DbTokenService(
        ISqlSugarClient db,
        IConfiguration configuration,
        IClock clock,
        IHttpContextAccessor httpContextAccessor)
    {
        _db = db;
        _configuration = configuration;
        _clock = clock;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// 创建新Token
    /// </summary>
    public async Task<UserTokenDto> CreateTokenAsync(long userId, string deviceInfo, string ipAddress, string userAgent)
    {
        // 获取JWT配置
        var jwtOptions = _configuration.GetRequiredSection("JwtOptions").Get<JwtOptions>() ?? new JwtOptions();

        // 创建Claims
        var claims = new List<Claim>
    {
        new Claim(AdminClaimConst.USER_ID, userId.ToString()),
        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
        new Claim("device_info", deviceInfo),
        new Claim("last_activity", _clock.Now.ToString("o"))
    };

        // 获取用户信息并添加到Claims
        var user = await _db.Queryable<UserEntity>().FirstAsync(u => u.Id == userId);
        if (user != null)
        {
            claims.Add(new Claim(AdminClaimConst.ACCOUNT, user.Account));
            claims.Add(new Claim(AdminClaimConst.NAME, user.Name));
            claims.Add(new Claim(AdminClaimConst.ORGANIZATION_ID, user.OrganizationId.ToString()));
            if (!string.IsNullOrEmpty(user.Email))
            {
                claims.Add(new Claim(ClaimTypes.Email, user.Email));
            }

            var roleIdList = (from s in _db.Queryable<UserRoleEntity>()
                              where s.UserId == userId
                              select s.RoleId).ToList();
            var roleNames = (from s in _db.Queryable<Role>()
                             where roleIdList.Contains(s.Id)
                             select s.RoleName).ToList();

            claims.Add(new Claim(ClaimTypes.Role, string.Join(",", roleNames)));
        }

        // 生成Token
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(jwtOptions.SecretKey);
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = _clock.Now.AddMinutes(jwtOptions.ExpiredMinutes),
            Issuer = jwtOptions.Issuer,
            Audience = jwtOptions.Audience,
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        // 生成刷新Token
        var refreshToken = GenerateRefreshToken();

        // 创建Token记录
        var userToken = new UserTokenEntity
        {
            UserId = userId,
            TokenValue = tokenString,
            RefreshToken = refreshToken,
            ExpireTime = _clock.Now.AddMinutes(jwtOptions.ExpiredMinutes),
            LastActivityTime = _clock.Now,
            DeviceInfo = deviceInfo,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            IsActive = true
        };

        // 保存到数据库
        await _db.Insertable(userToken).ExecuteReturnEntityAsync();

        // 转换为DTO
        return new UserTokenDto
        {
            Id = userToken.Id,
            UserId = userToken.UserId,
            TokenValue = userToken.TokenValue,
            RefreshToken = userToken.RefreshToken,
            ExpireTime = userToken.ExpireTime,
            LastActivityTime = userToken.LastActivityTime,
            DeviceInfo = userToken.DeviceInfo,
            IpAddress = userToken.IpAddress,
            UserAgent = userToken.UserAgent,
            IsActive = userToken.IsActive
        };
    }

    /// <summary>
    /// 验证Token
    /// </summary>
    public async Task<bool> ValidateTokenAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        // 查询数据库中的Token记录
        var tokenEntity = await _db.Queryable<UserTokenEntity>()
            .FirstAsync(t => t.TokenValue == token);

        // 如果Token不存在或已失效，返回false
        if (tokenEntity == null || !tokenEntity.IsActive)
            return false;

        // 如果Token已过期，返回false
        if (tokenEntity.ExpireTime < _clock.Now)
        {
            // 将过期Token标记为无效
            tokenEntity.IsActive = false;
            await _db.Updateable(tokenEntity).ExecuteCommandAsync();
            return false;
        }

        return true;
    }

    /// <summary>
    /// 撤销Token
    /// </summary>
    public async Task<bool> RevokeTokenAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        // 查询数据库中的Token记录
        var tokenEntity = await _db.Queryable<UserTokenEntity>()
            .FirstAsync(t => t.TokenValue == token);

        // 如果Token不存在，返回false
        if (tokenEntity == null)
            return false;

        // 将Token标记为无效
        tokenEntity.IsActive = false;
        await _db.Updateable(tokenEntity).ExecuteCommandAsync();

        return true;
    }

    /// <summary>
    /// 撤销用户所有Token
    /// </summary>
    public async Task<bool> RevokeAllUserTokensAsync(long userId)
    {
        // 将用户所有Token标记为无效
        await _db.Updateable<UserTokenEntity>()
            .SetColumns(t => new UserTokenEntity { IsActive = false })
            .Where(t => t.UserId == userId && t.IsActive)
            .ExecuteCommandAsync();

        return true;
    }

    /// <summary>
    /// 刷新Token
    /// </summary>
    public async Task<UserTokenDto> RefreshTokenAsync(string refreshToken)
    {
        // 查询数据库中的Token记录
        var tokenEntity = await _db.Queryable<UserTokenEntity>()
            .FirstAsync(t => t.RefreshToken == refreshToken && t.IsActive);

        // 如果刷新Token不存在或已失效，返回null
        if (tokenEntity == null)
            return null;

        // 将旧Token标记为无效
        tokenEntity.IsActive = false;
        await _db.Updateable(tokenEntity).ExecuteCommandAsync();

        // 创建新Token
        return await CreateTokenAsync(
            tokenEntity.UserId,
            tokenEntity.DeviceInfo,
            tokenEntity.IpAddress,
            tokenEntity.UserAgent);
    }

    /// <summary>
    /// 更新Token最后活动时间
    /// </summary>
    public async Task<bool> UpdateLastActivityAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        // 查询数据库中的Token记录
        var tokenEntity = await _db.Queryable<UserTokenEntity>()
            .FirstAsync(t => t.TokenValue == token && t.IsActive);

        // 如果Token不存在或已失效，返回false
        if (tokenEntity == null)
            return false;

        // 更新最后活动时间
        tokenEntity.LastActivityTime = _clock.Now;
        await _db.Updateable(tokenEntity).ExecuteCommandAsync();

        return true;
    }

    /// <summary>
    /// 获取用户所有有效Token
    /// </summary>
    public async Task<List<UserTokenDto>> GetUserActiveTokensAsync(long userId)
    {
        var tokens = await _db.Queryable<UserTokenEntity>()
            .Where(t => t.UserId == userId && t.IsActive && t.ExpireTime > _clock.Now)
            .ToListAsync();

        // 转换为DTO列表
        return tokens.Select(t => new UserTokenDto
        {
            Id = t.Id,
            UserId = t.UserId,
            TokenValue = t.TokenValue,
            RefreshToken = t.RefreshToken,
            ExpireTime = t.ExpireTime,
            LastActivityTime = t.LastActivityTime,
            DeviceInfo = t.DeviceInfo,
            IpAddress = t.IpAddress,
            UserAgent = t.UserAgent,
            IsActive = t.IsActive
        }).ToList();
    }

    /// <summary>
    /// 清理过期Token
    /// </summary>
    public async Task<int> CleanupExpiredTokensAsync()
    {
        // 将过期Token标记为无效
        return await _db.Updateable<UserTokenEntity>()
            .SetColumns(t => new UserTokenEntity { IsActive = false })
            .Where(t => t.ExpireTime < _clock.Now && t.IsActive)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 生成刷新Token
    /// </summary>
    private string GenerateRefreshToken()
    {
        var randomNumber = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }
}
