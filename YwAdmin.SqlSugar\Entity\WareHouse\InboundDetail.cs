﻿// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YwAdmin.SqlSugar.Entity.WareHouse;
[SugarTable("Inv_Inbound_Detail")]
public class InboundDetail :BaseEntity
{
	/// <summary>
	/// 入库单号
	/// </summary>
	public string ReceiptNo { get; set; }
	/// <summary>
	/// 序号
	/// </summary>
	public int Seq { get; set; }
	/// <summary>
	/// 原材料料号
	/// </summary>
	public string MCode { get; set; }
	/// <summary>
	/// 商品编号
	/// </summary>
	public string ItemCode { get; set; }
	/// <summary>
	/// 商品名称
	/// </summary>
	public string ItemName { get; set; }
	/// <summary>
	/// 规格型号
	/// </summary>
	public string SpecModel { get; set; }
	/// <summary>
	/// 单位
	/// </summary>
	public string Unit { get; set; }
	/// <summary>
	/// 数量
	/// </summary>
	public decimal? Qty { get; set; }
	/// <summary>
	/// 件数
	/// </summary>
	public int? PackageCount { get; set; }
	/// <summary>
	/// 毛重
	/// </summary>
	public decimal? GrossWeight { get; set; }
	/// <summary>
	/// 净重
	/// </summary>
	public decimal? NetWeight { get; set; }
	/// <summary>
	/// 体积
	/// </summary>
	public decimal? Volume { get; set; }
	/// <summary>
	/// 单价
	/// </summary>
	public decimal? UnitPrice { get; set; }
	/// <summary>
	/// 金额
	/// </summary>
	public decimal? Amount { get; set; }
	/// <summary>
	/// 币种
	/// </summary>
	public string Currency { get; set; }
	/// <summary>
	/// 入库时间
	/// </summary>
	public DateTime? ReceiptDate { get; set; }
	/// <summary>
	/// 供应商代码
	/// </summary>
	public string SupplierNo { get; set; }
	/// <summary>
	/// 物料类型
	/// </summary>
	public string InvType { get; set; }
	/// <summary>
	/// 仓库代码
	/// </summary>
	public string WarehouseCode { get; set; }
}
