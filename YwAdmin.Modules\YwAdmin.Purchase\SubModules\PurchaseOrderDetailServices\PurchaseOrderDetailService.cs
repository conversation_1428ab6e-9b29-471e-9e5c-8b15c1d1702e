// Copyright © 2024-present wzw

using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.SqlSugar;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.Purchase.PurchaseOrderDetailServices.Dtos;
using YwAdmin.Purchase.PurchaseOrderDetailServices.Input;
using Mapster;
using Volo.Abp;
using OfficeOpenXml;
using YwAdmin.Multiplex.Contracts.helper;

namespace YwAdmin.Purchase.PurchaseOrderDetailServices;

/// <summary>
/// PurchaseOrderDetail服务
/// </summary>
/// <param name="db">数据</param>
/// <param name="currentUser"></param>
/// <param name="userRepository">用户表</param>
/// <param name="purchaseOrderDetailRepository">PurchaseOrderDetail表</param>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.PURCHASE)]
public class PurchaseOrderDetailService(
    ISqlSugarClient db,
    ICurrentUser currentUser,
    Repository<UserEntity> userRepository,
    Repository<PurchaseOrderDetail> purchaseOrderDetailRepository
    ) : PurchaseServiceBase
{
    private readonly Repository<PurchaseOrderDetail> _purchaseOrderDetailRepository = purchaseOrderDetailRepository;
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<UserEntity> _userRepository = userRepository;
    private readonly ICurrentUser _currentUser = currentUser;

    /// <summary>
    /// 查询PurchaseOrderDetail数据（分页）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetPurchaseOrderDetailPage(PurchaseOrderDetailInput input)
    {
        var query = _db.Queryable<PurchaseOrderDetail>();

        // 应用条件查询
        // 如果需要添加条件，请在这里添加
        // if (!string.IsNullOrEmpty(input.SomeProperty))
        // {
        //     query = query.Where(x => x.SomeProperty.Contains(input.SomeProperty));
        // }

        // 应用分页
        var total = await query.CountAsync();
        var list = await query.OrderByDescending(x => x.CreateTime)
                             .Skip((input.PageIndex - 1) * input.PageSize)
                             .Take(input.PageSize)
                             .ToListAsync();

        var resultList = list.Select(
              result =>
              {
                  var dto = result.Adapt<PurchaseOrderDetailDto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  return dto;
              });

        return new TableData
        {
            Data = resultList
        };
    }

    /// <summary>
    /// 查询所有PurchaseOrderDetail数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetPurchaseOrderDetailAll(PurchaseOrderDetailInput input)
    {
        var query = _db.Queryable<PurchaseOrderDetail>();

        // 应用条件查询
        // 如果需要添加条件，请在这里添加
        if (!string.IsNullOrEmpty(input.OrderNo))
        {
            query = query.Where(x => x.OrderNo.Contains(input.OrderNo));
        }

        var list = await query.OrderBy(x => x.Seq).ToListAsync();
        var resultList = list.Select(
              result =>
              {
                  var dto = result.Adapt<PurchaseOrderDetailDto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  return dto;
              });

        return new TableData
        {
            Data = resultList
        };
    }

    /// <summary>
    /// 新增或者更新PurchaseOrderDetail
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    ///
    [HttpPost]
    public async Task<TableData> AddOrEditPurchaseOrderDetail(PurchaseOrderDetailInput input)
    {
        if (input.Id == null)
        {
            var entity = input.Adapt<PurchaseOrderDetail>();
            await _purchaseOrderDetailRepository.InsertAsync(entity);
        }
        else
        {
            var purchaseOrderDetailInfo = await _db.Queryable<PurchaseOrderDetail>().FirstAsync(x => x.Id == input.Id);
            if (purchaseOrderDetailInfo != null)
            {
                // Update properties here
                await _purchaseOrderDetailRepository.UpdateAsync(purchaseOrderDetailInfo);
            }
            else
            {
                throw new UserFriendlyException(L["DataException"]);
            }
        }
        return new TableData { Data = L["SaveSuccessfule"] };
    }

    /// <summary>
    /// 查询PurchaseOrderDetail明细数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> GetPurchaseOrderDetail(PurchaseOrderDetailInput input)
    {
        if (input.Id == null | input.Id == 0)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        var entity = await _db.Queryable<PurchaseOrderDetail>().FirstAsync(x => x.Id == input.Id);
        var result = entity.Adapt<PurchaseOrderDetailDto>();

        result.CreateUserName = _userRepository.GetById(entity.CreateUserId)?.Name;
        result.UpdateUserName = _userRepository.GetById(entity.UpdateUserId)?.Name;
        return new TableData
        {
            Data = result,
        };
    }

    /// <summary>
    /// 下载Excel导入模版
    /// </summary>
    /// <returns></returns>
    /// 
    [HttpPost]
    public async Task<TableData> DownLoadExcel()
    {
        try
        {
            // 创建文件流并使用 using 语句自动管理资源
            using (var fileStream = new MemoryStream())
            {
                using (var package = new ExcelPackage(fileStream))
                {
                    // 创建工作表
                    var worksheet = package.Workbook.Worksheets.Add("导入料件信息");

                    // 设置列头
                    var headers = new string[]
                    {
                    "原商品料号(必填)",
                    "原材料名称(必填)",
                    "内部编号",
                    "规格型号",
                    "取套",
                    "拖布长度",
                    "原始单耗",
                    "损耗率",
                    "单耗",
                    "客供取套",
                    "客供拖布长度",
                    "客供单耗",
                    "整版套数",
                    "有效使用率"
                    };

                    // 设置列头
                    for (int i = 0; i < headers.Length; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = headers[i];
                    }

                    // 自动调整列宽
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // 保存文件内容
                    package.Save();
                }

                fileStream.Position = 0;  // 重置流位置
                var fileData = fileStream.ToArray(); ;
                // 返回 TableData
                return new TableData
                {
                    Data = new
                    {
                        FileContent = fileData, // 返回字节数组
                        FileName = "采购导入模版.xlsx",
                        MimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    }
                };
            }
        }
        catch (Exception ex)
        {
            // 记录异常并返回错误信息
            return new TableData
            {
                Code = 500,
                Message = "Failed to generate Excel template: " + ex.Message
            };
        }
    }

}