// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.Warehouse.InvInStorage.Input;

/// <summary>
/// Inbound表输入模型
/// </summary>
public class InboundInput : PaginationParams
{
    /// <summary>
    /// ID
    /// </summary>
    public long? Id { get; set; }

	/// <summary>
	/// 入库单号
	/// </summary>
	public string ReceiptNo { get; set; }
	/// <summary>
	/// 入库时间
	/// </summary>
	public DateTime? Receiptdate { get; set; }
	/// <summary>
	/// 供应商代码
	/// </summary>
	public string Supplierno { get; set; }
	/// <summary>
	/// 类型
	/// </summary>
	public string Invtype { get; set; }
	/// <summary>
	/// 订单号
	/// </summary>
	public string Orderno { get; set; }
	/// <summary>
	/// 仓库代码
	/// </summary>
	public string Warehousecode { get; set; }
}
