namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 组织机构
/// </summary>
[SugarTable("SYS_ORGANIZATION")]
public partial class OrganizationEntity
{
	/// <summary>
	/// 主键Id
	/// </summary>
	[SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
	public long Id { get; set; }
	/// <summary>
	/// 创建人
	/// </summary>
	[SugarColumn(ColumnName = "CREATEUSERID")]
	public long Createuserid { get; set; }
	/// <summary>
	/// 创建时间
	/// </summary>
	[SugarColumn(ColumnName = "CREATETIME")]
	public DateTime Createtime { get; set; }
	/// <summary>
	/// 修改人
	/// </summary>
	[SugarColumn(ColumnName = "UPDATATEUSERID")]
	public long? Updatateuserid { get; set; }
	/// <summary>
	/// 修改时间
	/// </summary>
	[SugarColumn(ColumnName = "UPDATETIME")]
	public DateTime? Updatetime { get; set; }
	/// <summary>
	/// 备注
	/// </summary>
	[SugarColumn(ColumnName = "REMARK")]
	public string Remark { get; set; }
	/// <summary>
	/// 名称
	/// </summary>
	[SugarColumn(ColumnName = "NAME")]
	public string Name { get; set; }
	/// <summary>
	/// 父级ID
	/// </summary>
	[SugarColumn(ColumnName = "PARENT_ID")]
	public long? ParentId { get; set; }
	/// <summary>
	/// 联系电话
	/// </summary>
	[SugarColumn(ColumnName = "TELEPHONE")]
	public string Telephone { get; set; }
	/// <summary>
	/// 负责人
	/// </summary>
	[SugarColumn(ColumnName = "LEADER")]
	public string Leader { get; set; }
	/// <summary>
	/// 排序
	/// </summary>
	[SugarColumn(ColumnName = "SORT")]
	public int? Sort { get; set; }
}