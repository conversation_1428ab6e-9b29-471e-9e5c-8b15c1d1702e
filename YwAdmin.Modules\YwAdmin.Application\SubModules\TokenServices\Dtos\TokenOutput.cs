

namespace YwAdmin.Application.TokenServices.Dtos;

/// <summary>
/// Token输出模型
/// </summary>
public class TokenOutput
{
    /// <summary>
    /// Token ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 设备信息
    /// </summary>
    public string DeviceInfo { get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; set; }
    
    /// <summary>
    /// 用户代理
    /// </summary>
    public string UserAgent { get; set; }
    
    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityTime { get; set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime ExpireTime { get; set; }
}
