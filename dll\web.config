<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\YwAdmin.Api.Host.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
        <rewrite>
        <rules>
            <rule name="FilesAccess" stopProcessing="true">
                <match url="^api/v1/files/(.*)" />
                <conditions>
                    <add input="{HTTP_HOST}" pattern="localhost:5777" />
                    <add input="{HTTP_X_FORWARDED_FOR}" pattern=".*" negate="true" />
                </conditions>
                <action type="Rewrite" url="http://localhost:5062/files/{R:1}" />
                <serverVariables>
                    <set name="HTTP_X_ORIGINAL_HOST" value="{HTTP_HOST}" />
                    <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
                </serverVariables>
            </rule>
            <rule name="ReverseProxy" stopProcessing="true">
                <match url="^api/v1/(.*)" />
                <conditions>
                    <add input="{HTTP_HOST}" pattern="localhost:5777" />
                    <add input="{HTTP_X_FORWARDED_FOR}" pattern=".*" negate="true" />
                </conditions>
                <action type="Rewrite" url="http://localhost:5062/api/v1/{R:1}" />
                <serverVariables>
                    <set name="HTTP_X_ORIGINAL_HOST" value="{HTTP_HOST}" />
                    <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
                </serverVariables>
            </rule>
        </rules>
    </rewrite>
    </system.webServer>
  </location>
</configuration>