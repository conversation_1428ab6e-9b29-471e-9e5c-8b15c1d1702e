﻿namespace YwAdmin.SqlSugar.Entity;

/// <summary>
/// 数据字典参数文本
/// </summary>
[SugarTable("SYS_DICTIONARY_CODE")]
public partial class DictionaryCode : BaseEntity
{
    /// <summary>
    /// 值字段
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    /// 文本字段
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    /// 序列
    /// </summary>
    public string Seq { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnable { get; set; }

    /// <summary>
    /// 是否默认
    /// </summary>
    public bool? IsDefault { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 代码类型
    /// </summary>
    public string CodeType { get; set; }

    /// <summary>
    /// 代码类型名称
    /// </summary>
    public string CodeTypeName { get; set; }
}