--【2025-3-9】--添加数据字典-Bom状态
if not EXISTS(select CodeType from SYS_DICTIONARY_CODETYPE where  CodeType='BomStatus')
insert into SYS_DICTIONARY_CODETYPE (CodeType,CodeTypeName,Seq,CREATETIME,CREATEUSERID)values ('BomStatus','BOM状态',10,GETDATE(),1)

if not EXISTS(select 1 from SYS_DICTIONARY_CODE where  CodeType='new' and value = '新建')
insert into SYS_DICTIONARY_CODE (Value,Text,Seq,IsEnable,IsDefault,CodeType,CodeTypeName,CREATETIME,CREATEUSERID)
values ('new','新建',1,1,0,'BomStatus','BOM状态',GETDATE(),1)

if not EXISTS(select 1 from SYS_DICTIONARY_CODE where  CodeType='release' and value = '发布')
insert into SYS_DICTIONARY_CODE (Value,Text,Seq,IsEnable,IsDefault,CodeType,CodeTypeName,CREATETIME,CREATEUSERID)
values ('release','发布',2,1,0,'BomStatus','BOM状态',GETDATE(),1)

if not EXISTS(select 1 from SYS_DICTIONARY_CODE where  CodeType='audit' and value = '审核中')
insert into SYS_DICTIONARY_CODE (Value,Text,Seq,IsEnable,IsDefault,CodeType,CodeTypeName,CREATETIME,CREATEUSERID)
values ('audit','审核中',3,1,0,'BomStatus','BOM状态',GETDATE(),1)

