﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>1701;1702;1591;8618;8602;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="SubModules\BasicServices\Dtos\**" />
    <EmbeddedResource Remove="SubModules\BasicServices\Dtos\**" />
    <None Remove="SubModules\BasicServices\Dtos\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Localization\Application\en.json" />
    <None Remove="Localization\Application\vi.json" />
    <None Remove="Localization\Application\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\Application\en.json" />
    <EmbeddedResource Include="Localization\Application\vi.json" />
    <EmbeddedResource Include="Localization\Application\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\YwAdmin.Core\YwAdmin.Core.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex.Contracts\YwAdmin.Multiplex.Contracts.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex\YwAdmin.Multiplex.csproj" />
    <ProjectReference Include="..\..\YwAdmin.SqlSugar\YwAdmin.SqlSugar.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="System.ComponentModel.DataAnnotations" />
  </ItemGroup>
</Project>
