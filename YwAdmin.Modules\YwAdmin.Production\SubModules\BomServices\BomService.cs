using LiteDB;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Namotion.Reflection;
using Newtonsoft.Json;
using OfficeOpenXml;
using SixLabors.ImageSharp.Formats.Jpeg;
using SqlSugar;
using System.Data;
using System.Dynamic;
using Volo.Abp;
using YwAdmin.Core.Extensions;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.Multiplex.Contracts.Dto;
using YwAdmin.Multiplex.Contracts.helper;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Production.BomServices.Dtos;
using YwAdmin.Production.SubModules.BomServices.Dtos;
using YwAdmin.SqlSugar;
using YwAdmin.SqlSugar.Entity;

namespace YwAdmin.Production.BomServices;
/// <summary>
/// 技术单耗表服务
/// </summary>
///

[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.PRODUCTION)]
public class BomService(
    ISqlSugarClient db,
    Repository<Items> tucfpRepository,
    ICurrentUser currentUser,
    Repository<UserEntity> userRepository,
    Repository<Material> tcmRepository,
    Repository<TechConsumption> techConsumptionRepository
    ) : ProductionServiceBase
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<Items> _tucfpRepository = tucfpRepository;
    private readonly ICurrentUser _currentUser = currentUser;
    private readonly Repository<UserEntity> _userRepository = userRepository;
    private readonly Repository<TechConsumption> _techConsumptionRepository = techConsumptionRepository;
    private readonly Repository<Material> _tcmRepository = tcmRepository;

    /// <summary>
    /// 查询技术单耗成品所有信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetTUCFPList(TUCFPInput input)
    {
        var query = db.Queryable<Items>();
        if (!string.IsNullOrEmpty(input.Seat))
        {
            query = query.Where(x => x.Seat.Contains(input.Seat));
        }
        if (!string.IsNullOrEmpty(input.Remark))
        {
            query = query.Where(x => x.Remark.Contains(input.Remark));
        }
        if (!string.IsNullOrEmpty(input.SeriesNo))
        {
            query = query.Where(x => x.SeriesNo.Contains(input.SeriesNo));
        }
        var results = await query
               .ToListAsync();
        var resultList = results.Select(
               result =>
               {
                   var dto = result.Adapt<TUCFPOutput>();
                   dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                   dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                   return dto;
               }).ToList();
        return new TableData { Data = resultList };
    }

    /// <summary>
    /// 查询单条技术单耗成品信息
    /// </summary>
    /// <param name="TUCFPInput"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetTucfpInfo(TUCFPInput input)
    {
        if (input.Id == null)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        var entity = await _tucfpRepository.GetFirstAsync(x => x.Id == input.Id);
        var result = entity.Adapt<TUCFPOutput>();
        result.CreateUserName = _userRepository.GetById(entity.CreateUserId)?.Name;
        result.UpdateUserName = _userRepository.GetById(entity.UpdateUserId)?.Name;
        return new TableData
        {
            Data = result,
        };
    }

    /// <summary>
    /// 新增技术单耗成品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<TableData> TUCFPAddOrEdit(AddTUCFPInput input)
    {
        try
        {
            long id = 0;
            if (input.Id == null)
            {
                //成品编号 格式 **********
                string newId = "";
                var strDate = "CP" + DateTime.Now.ToString("yyyyMM");
                var maxId = await _db.Queryable<Items>().Where(x => x.FGCode.Contains(strDate)).OrderByDescending(x => x.FGCode).Select(x => x.FGCode).FirstAsync();
                if (string.IsNullOrEmpty(maxId))
                {
                    newId = strDate + "01";
                }
                else
                {
                    string fgCodeEnd = maxId.Substring(maxId.Length - 2); // 获取最后两位
                    if (int.TryParse(fgCodeEnd, out int lastTwoDigits))
                    {
                        int newFGCode = lastTwoDigits + 1; // 加 1
                        newId = strDate + newFGCode.ToString().PadLeft(2, '0'); // 确保两位数
                    }
                }
                input.FGCode = newId;
                var entity = input.Adapt<Items>();
                entity.Status = "new";
                entity.CreateTime = DateTime.Now;
                entity.CreateUserId = _currentUser.GetUserId();
                entity.UpdateTime = DateTime.Now;
                entity.UpdateUserId = _currentUser.GetUserId();
                id = await _tucfpRepository.InsertReturnIdAsync(entity);
                dynamic result = new ExpandoObject();
                result.id = id;
                result.fgCode = newId;
                return new TableData
                {
                    Data = result
                };
            }
            else
            {
                var tucfpInfo = await _tucfpRepository.GetFirstAsync(x => x.Id == input.Id);
                if (tucfpInfo != null)
                {
                    if (tucfpInfo.Status != "new" && tucfpInfo.Status != "cancel")
                    {
                        throw new UserFriendlyException(L["StatusErro3"]);
                    }
                    tucfpInfo.Seat = input.Seat;
                    tucfpInfo.SeriesNo = input.SeriesNo;
                    tucfpInfo.StyleNo = input.StyleNo;
                    tucfpInfo.VersionNo = input.VersionNo;
                    tucfpInfo.TargetMarket = input.TargetMarket;
                    tucfpInfo.UnitWeightKg = input.UnitWeightKg;
                    tucfpInfo.ProductNature = input.ProductNature;
                    tucfpInfo.StorageMethod = input.StorageMethod;
                    tucfpInfo.StorageCount = input.StorageCount;
                    tucfpInfo.BoxWeight = input.BoxWeight;
                    tucfpInfo.Valid = input.Valid;
                    tucfpInfo.DomesticExport = input.DomesticExport;
                    tucfpInfo.UnitPrice = input.UnitPrice;
                    tucfpInfo.Remark = input.Remark;
                    tucfpInfo.FGItemNo = input.FGItemNo;
                    tucfpInfo.ProductCode = input.ProductCode;
                    tucfpInfo.ProductName = input.ProductName;
                    tucfpInfo.SpecModel = input.SpecModel;
                    tucfpInfo.Unit = input.Unit;
                    tucfpInfo.SalesCountry = input.SalesCountry;
                    tucfpInfo.UpdateUserId = _currentUser.GetUserId();
                    tucfpInfo.UpdateTime = DateTime.Now;
                    await _tucfpRepository.UpdateAsync(tucfpInfo);
                }
                else
                {
                    throw new UserFriendlyException(L["DataException"]);
                }
            }
            return new TableData
            {
            };
        }
        catch (Exception exc)
        {
            throw new UserFriendlyException(exc.Message);
        }
    }

    /// <summary>
    /// 删除技术单耗成品信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> DeleteTucfpInfo(TUCFPInput input)
    {
        try
        {
            if (input.Id == null)
            {
                throw new UserFriendlyException(L["DataException"]);
            }
            var tucfp = await _tucfpRepository.GetFirstAsync(x => x.Id == input.Id);
            if (tucfp == null)
            {
                throw new UserFriendlyException(L["DeletedData"]);
            }
            if (tucfp.Status != "new" && tucfp.Status != "cancel")
            {
                throw new UserFriendlyException(L["StatusNotDel"]);
            }
            //根据成品编号获取单单耗，如果存在不能删除
            var techInfo = await _techConsumptionRepository.GetFirstAsync(x => x.FGCode == tucfp.FGCode);
            if (techInfo != null)
            {
                throw new UserFriendlyException(L["HasTechNotDel"]);
            }
            await _tucfpRepository.DeleteAsync(x => x.Id == input.Id);

            return new TableData { };
        }
        catch (Exception exc)
        {
            throw new UserFriendlyException(exc.Message);
        }
    }

    /// <summary>
    /// 查询成品编号对应的料卷信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<TableData> GetTechConsumptionList(GetPagedListInput input)
    {
        var query = db.Queryable<TechConsumption>();
        if (!string.IsNullOrEmpty(input.FGCode))
        {
            query = query.Where(x => x.FGCode == input.FGCode);
        }

        var results = await query
               .ToListAsync();
        var resultList = results.Select(
               result =>
               {
                   var dto = result.Adapt<TechConsumptionOutput>();
                   return dto;
               }).ToList();
        return new TableData { Data = resultList };
    }

    /// <summary>
    /// 查询料件基础信息（）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetMaterialListByFG(TCMInput input)
    {
        if (string.IsNullOrEmpty(input.FGCode))
        {
            return new TableData
            {
                Data = new List<TCMDto>()
            };
        }
        var query = _db.Queryable<Material>().LeftJoin<TechConsumption>((x, o) => x.MCode == o.MCode && o.FGCode == input.FGCode)
              .Where((x, o) => o.Id == null); // 只选没有匹配的数据

        if (!string.IsNullOrEmpty(input.MCode))
        {
            query = query.Where(x => x.MCode.Contains(input.MCode));
        }
        if (!string.IsNullOrEmpty(input.MName))
        {
            query = query.Where(x => x.MName.Contains(input.MName));
        }
        if (!string.IsNullOrEmpty(input.Ids))
        {
            List<long> idList = input.Ids.Split(',').Select(id => long.Parse(id)).ToList();
            query = query.Where(x => !idList.Contains(x.Id));
        }

        var result = await query.ToListAsync();
        var resultList = result.Adapt<List<TCMDto>>();
        return new TableData
        {
            Data = resultList
        };
    }

    /// <summary>
    /// 查询所有料件信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetMaterialList(TCMInput input)
    {
        var query = _db.Queryable<Material>(); // 只选没有匹配的数据

        if (!string.IsNullOrEmpty(input.MCode))
        {
            query = query.Where(x => x.MCode.Contains(input.MCode));
        }
        if (!string.IsNullOrEmpty(input.MName))
        {
            query = query.Where(x => x.MName.Contains(input.MName));
        }
        var total = await query.CountAsync();
        var result = await query
            .InputPage(input)
            .ToListAsync();
        
        var resultList = result.Adapt<List<TCMDto>>();
        return new TableData
        {
            Data = resultList,
            TotalCount = total
        };
    }

    /// <summary>
    /// 发布料件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> ReleaseMaterial(TCMInput input)
    {
        //获取到料件ID
        var tcmInfo = await _tcmRepository.GetFirstAsync(x => x.Id == input.Id);
        if (tcmInfo == null)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        if (tcmInfo.Status != "new")
        {
            throw new UserFriendlyException(L["StatusErro"]);
        }
        tcmInfo.Status = "release";
        tcmInfo.Reviewer = _currentUser.GetUsername();
        tcmInfo.TimeReviewed = DateTime.Now;
        await _tcmRepository.UpdateAsync(tcmInfo);
        return new TableData { Data = L["UpdateSuccessful"] };
    }

    /// <summary>
    /// 取消料件发布
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> CancelReleaseMaterial(TCMInput input)
    {
        //获取到料件ID
        var tcmInfo = await _tcmRepository.GetFirstAsync(x => x.Id == input.Id);
        if (tcmInfo == null)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        if (tcmInfo.Status != "release")
        {
            throw new UserFriendlyException(L["CancelStatusErro"]);
        }
        tcmInfo.Status = "new";
        await _tcmRepository.UpdateAsync(tcmInfo);
        return new TableData { Data = L["UpdateSuccessful"] };
    }

    /// <summary>
    /// 新增技术单耗料件信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> TcmAddOrEdit(AddTCMDto input)
    {
        try
        {
            if (input.Id == null || input.Id == 0)
            {
                if (string.IsNullOrEmpty(input.MCode))
                {
                    throw new UserFriendlyException(L["MCodeNull"]);
                }
                var mCodeInfo = await _db.Queryable<Material>().FirstAsync(x => x.MCode == input.MCode);
                if (mCodeInfo != null)
                {
                    throw new UserFriendlyException(L["MCodeHas"]);
                }
                var entity = input.Adapt<Material>();
                entity.Status = "new";
                entity.CreateTime = DateTime.Now;
                entity.CreateUserId = _currentUser.GetUserId();
                entity.UpdateTime = DateTime.Now;
                entity.UpdateUserId = _currentUser.GetUserId();
                await _db.Insertable(entity).ExecuteReturnSnowflakeIdAsync();
            }
            else
            {
                var tcmInfo = await _db.Queryable<Material>().FirstAsync(x => x.Id == input.Id);
                if (tcmInfo != null)
                {
                    if (tcmInfo.Status != "new")
                    {
                        throw new UserFriendlyException(L["MCodeStatus"]);
                    }
                    tcmInfo.MCode = input.MCode;
                    tcmInfo.MName = input.MName;
                    tcmInfo.UnitPrice = input.UnitPrice;
                    tcmInfo.ProductCode = input.ProductCode;
                    tcmInfo.ProductName = input.ProductName;
                    tcmInfo.SpecModel = input.SpecModel;
                    tcmInfo.Unit = input.Unit;
                    tcmInfo.Obsolete = input.Obsolete;
                    tcmInfo.GSM = input.GSM;
                    tcmInfo.IntCode = input.IntCode;
                    tcmInfo.Width = input.Width;
                    tcmInfo.MatItemNo = input.MatItemNo;
                    tcmInfo.OriginCountry = input.OriginCountry;
                    tcmInfo.SingleWeight = input.SingleWeight;
                    //tcmInfo.TradeMode = input.TradeMode;

                    tcmInfo.UpdateUserId = _currentUser.GetUserId();
                    tcmInfo.UpdateTime = DateTime.Now;
                    await _db.Updateable(tcmInfo).ExecuteCommandAsync();
                }
                else
                {
                    throw new UserFriendlyException(L["DataException"]);
                }
            }

            return new TableData
            {
            };
        }
        catch (Exception exc)
        {
            throw new UserFriendlyException(exc.Message);
        }
    }

    /// <summary>
    /// 查询单条技术单耗料件信息
    /// </summary>
    /// <param name="TUCFPInput"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetTCMInfo(TCMInput input)
    {
        if (input.Id == null | input.Id == 0)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        var entity = await _db.Queryable<Material>().FirstAsync(x => x.Id == input.Id);
        var result = entity.Adapt<TCMDto>();
        result.CreateUserName = _userRepository.GetById(entity.CreateUserId)?.Name;
        result.UpdateUserName = _userRepository.GetById(entity.UpdateUserId)?.Name;
        return new TableData
        {
            Data = result,
        };
    }

    /// <summary>
    /// 删除技术单耗料件信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> DeleteTCMInfo(TUCFPInput input)
    {
        try
        {
            var entity = await _db.Queryable<Material>().FirstAsync(x => x.Id == input.Id);
            if (entity == null)
            {
                throw new UserFriendlyException(L["DataException"]);
            }
            if (entity.Status != "new")
            {
                throw new UserFriendlyException(L["DelMCodeStatus"]);
            }
            var techInfo = await _techConsumptionRepository.GetFirstAsync(x => x.MCode == entity.MCode);
            if (techInfo != null)
            {
                throw new UserFriendlyException(L["MCodeUseNotDel"]);
            }
            await _db.Deleteable(entity).ExecuteCommandAsync();
            return new TableData { };
        }
        catch (Exception)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
    }

    /// <summary>
    /// 针对产品编号原商品料号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<TableData> InsertMcodeByFg(AddTechConsumptionInput input)
    {
        //获取到Items，是json数据，解析成 AddTechConsumptionInput
        List<AddTechConsumptionInput> itemList = string.IsNullOrEmpty(input.Items) ? new List<AddTechConsumptionInput>() : JsonConvert.DeserializeObject<List<AddTechConsumptionInput>>(input.Items) ?? new List<AddTechConsumptionInput>();

        foreach (var item in itemList)
        {
            var tcmInfo = await _db.Queryable<Material>().FirstAsync(x => x.Id == item.Id);
            if (tcmInfo.Status != "release")
            {
                tcmInfo.Status = "release";
                await _tcmRepository.UpdateAsync(tcmInfo);
            }
            var tech = new TechConsumption();
            tech.MCode = tcmInfo.MCode;
            tech.MName = tcmInfo.MName;
            tech.IntCode = tcmInfo.IntCode;
            tech.SpecModel = tcmInfo.SpecModel;
            tech.FGCode = input.FGCode;
            await _db.Insertable(tech).ExecuteReturnSnowflakeIdAsync();
            //同步更新料件为发布状态
        }
        //foreach (var id in idList)
        //{
        //    var tcmInfo = await _db.Queryable<Material>().FirstAsync(x => x.Id == id);

        //}
        return new TableData
        {
        };
    }

    /// <summary>
    /// 发布Bom
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> ReleaseBom(TUCFPInput input)
    {
        //判断当前FGCode是否存在、
        if (string.IsNullOrEmpty(input.FgCodes))
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        List<string> fgCodeList = input.FgCodes.Split(',').ToList();
        foreach (var fgCode in fgCodeList)
        {
            if (string.IsNullOrEmpty(fgCode))
            {
                continue;
            }
            //查找FGCode的对应数据
            var tucfpInfo = await _db.Queryable<Items>().FirstAsync(x => x.FGCode == fgCode);
            if (tucfpInfo == null)
            {
                throw new UserFriendlyException(L["DataException"]);
            }
            if (tucfpInfo.Status != "new" && tucfpInfo.Status != "cancel")
            {
                throw new UserFriendlyException(L["StatusErro"]);
            }
            var userInfo = _currentUser.GetUserInfo();
            tucfpInfo.Status = "audit";
            tucfpInfo.Publisher = userInfo.Name;
            tucfpInfo.PublishDate = DateTime.Now;
            //await _db.Updateable(tucfpInfo).ExecuteCommandAsync();
            await _tucfpRepository.UpdateAsync(tucfpInfo);
        }
        return new TableData { Data = L["UpdateSuccessful"] };
    }

    /// <summary>
    /// 取消发布Bom
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> CancelReleaseBom(TUCFPInput input)
    {
        //判断当前FGCode是否存在、
        if (string.IsNullOrEmpty(input.FgCodes))
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        List<string> fgCodeList = input.FgCodes.Split(',').ToList();
        foreach (var fgCode in fgCodeList)
        {
            if (string.IsNullOrEmpty(fgCode))
            {
                continue;
            }
            //查找FGCode的对应数据
            var tucfpInfo = await _db.Queryable<Items>().FirstAsync(x => x.FGCode == fgCode);
            if (tucfpInfo == null)
            {
                throw new UserFriendlyException(L["DataException"]);
            }
            if (tucfpInfo.Status != "audit" && tucfpInfo.Status != "release")
            {
                throw new UserFriendlyException(L["CancelStatusErro"]);
            }
            var userInfo = _currentUser.GetUserInfo();
            tucfpInfo.Status = "cancel";
            tucfpInfo.Reviewer = userInfo.Name;
            tucfpInfo.ReviewDate = DateTime.Now;

            //await _db.Updateable(tucfpInfo).ExecuteCommandAsync();
            await _tucfpRepository.UpdateAsync(tucfpInfo);
        }
        return new TableData { Data = L["UpdateSuccessful"] };
    }

    /// <summary>
    /// 审核Bom
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> AuditBom(TUCFPInput input)
    {
        //判断当前FGCode是否存在、
        if (string.IsNullOrEmpty(input.FgCodes))
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        List<string> fgCodeList = input.FgCodes.Split(',').ToList();
        foreach (var fgCode in fgCodeList)
        {
            if (string.IsNullOrEmpty(fgCode))
            {
                continue;
            }
            //查找FGCode的对应数据
            var tucfpInfo = await _db.Queryable<Items>().FirstAsync(x => x.FGCode == fgCode);
            if (tucfpInfo == null)
            {
                throw new UserFriendlyException(L["DataException"]);
            }
            if (tucfpInfo.Status != "audit")
            {
                throw new UserFriendlyException(L["StatusErro2"]);
            }
            var userInfo = _currentUser.GetUserInfo();
            tucfpInfo.Status = "release";
            tucfpInfo.Reviewer = userInfo.Name;
            tucfpInfo.ReviewDate = DateTime.Now;

            var tucfpList = await _tucfpRepository.GetListAsync();

            //await _db.Updateable(tucfpInfo).ExecuteCommandAsync();
            await _tucfpRepository.UpdateAsync(tucfpInfo);
        }

        return new TableData { Data = L["UpdateSuccessful"] };
    }

    /// <summary>
    /// 选择删除料件对应物料信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<TableData> DeleteBomMCode(PutTechConsumptionInput input)
    {
        if (string.IsNullOrEmpty(input.Ids))
        {
            return new TableData { Data = L["Abnormal"] };
        }
        //解析Items：id使用,隔开
        var IdList = input.Ids.Split(',').Select(id => long.Parse(id)).ToList();
        foreach (var id in IdList)
        {
            await _techConsumptionRepository.DeleteAsync(x => x.Id == id);
        }
        return new TableData { Data = L["UpdateSuccessful"] };
    }

    /// <summary>
    /// BOM导入
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    public async Task<TableData> ImportTechByExcel([FromForm] PutTechConsumptionInput input)
    {
        var resultList = new List<BomExcelDto>();

        using (var package = new ExcelPackage(input.InputStream.OpenReadStream()))
        {
            ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
            resultList = GetTechmWorksheet(worksheet);
        }

        return new TableData { Data = resultList };
    }

    private List<BomExcelDto> GetTechmWorksheet(ExcelWorksheet worksheet)
    {
        var itemLotExcelList = new List<BomExcelDto>();
        //循环第一行，获取到指定的列名的列数
        var columnMapping = new Dictionary<string, int>();
        var columnNames = new List<string> { "父项物料编码", "物料名称", "规格型号", "子项物料编码", "子项物料名称", "子项规格型号", "取套数", "单耗", "有效使用率", "拖布长度" };
        for (int col = worksheet.Dimension.Start.Column; col <= worksheet.Dimension.End.Column; col++)
        {
            string columnName = worksheet.Cells[2, col].Text.Trim(); // 读取表头
            if (columnNames.Contains(columnName) && !columnMapping.ContainsKey(columnName))
            {
                columnMapping[columnName] = col; // 记录列索引
            }
        }

        for (var row = worksheet.Dimension.Start.Row + 2; row <= worksheet.Dimension.End.Row; row++)
        {
            var itemExcel = new BomExcelDto();
            var strErrorMsg = string.Empty;
            //Bom部分
            itemExcel.ItemMCode = worksheet.Cells[row, columnMapping["父项物料编码"]].Value?.ToString();//商品料号
            itemExcel.SeriesNo = worksheet.Cells[row, columnMapping["物料名称"]].Value?.ToString();//系列号
            itemExcel.StyleNo = worksheet.Cells[row, columnMapping["规格型号"]].Value?.ToString();//款号
            //检查是否存相同的bom商品料号
            if (string.IsNullOrEmpty(itemExcel.ItemMCode))
            {
                //获取到itemLotExcelList最后的一个ItemCode
                var lastItemCode = itemLotExcelList.LastOrDefault();
                if (lastItemCode != null)
                {
                    itemExcel.ItemMCode = lastItemCode.ItemMCode;
                    itemExcel.SeriesNo = lastItemCode.SeriesNo;
                    itemExcel.StyleNo = lastItemCode.StyleNo;
                }
            }
            else
            {
                var itemInfo = _tucfpRepository.GetFirst(x => x.ProdMatCode == itemExcel.ItemMCode);
                if (itemInfo != null)
                {
                    strErrorMsg += "父项物料编码已在系统存在,";
                }
            }
            //单耗管理
            itemExcel.IntCode = worksheet.Cells[row, columnMapping["子项物料编码"]].Value?.ToString();//内部编号
            itemExcel.MName = worksheet.Cells[row, columnMapping["子项物料名称"]].Value?.ToString();//原材料名称
            itemExcel.MCode = worksheet.Cells[row, columnMapping["子项规格型号"]].Value?.ToString();//商品料号
            //检查判断MCode是否为空必填
            if (!string.IsNullOrEmpty(itemExcel.MCode))
            {
                var tcmInfo = _tcmRepository.GetFirst(x => x.MCode == itemExcel.MCode);
                if (tcmInfo != null)
                {
                    if (tcmInfo.MName != itemExcel.MName || tcmInfo.IntCode != itemExcel.IntCode)
                    {
                        strErrorMsg += "料件信息不匹配,";
                    }
                }
                else
                {
                    strErrorMsg += "料件信息不存在系统中,";
                }
            }
            else
            {
                strErrorMsg += "子项规格型号不能为空,";
            }
            if (string.IsNullOrEmpty(itemExcel.MCode) && string.IsNullOrEmpty(itemExcel.MName) && string.IsNullOrEmpty(itemExcel.IntCode))
            {
                break;
            }

            //原材料
            itemExcel.Casing = ConvertObj.Obj2Decimal(worksheet.Cells[row, columnMapping["取套数"]].Value);//取套
            itemExcel.OriginalUnit = ConvertObj.Obj2Decimal(worksheet.Cells[row, columnMapping["单耗"]].Value);//原始单耗
            itemExcel.EffectiveUsageRate = ConvertObj.Obj2Decimal(worksheet.Cells[row, columnMapping["有效使用率"]].Value);//有效使用率
            itemExcel.MopLength = ConvertObj.Obj2Decimal(worksheet.Cells[row, columnMapping["拖布长度"]].Value);//拖布长度

            itemExcel.ErrorMsg = strErrorMsg.Length > 0
                ? strErrorMsg.Substring(0, strErrorMsg.LastIndexOf(','))
                : strErrorMsg;
            itemExcel.HasErrorMsg = !string.IsNullOrEmpty(itemExcel.ErrorMsg);
            itemLotExcelList.Add(itemExcel);
        }

        return itemLotExcelList;
    }

    /// <summary>
    /// 通过Excel导入，保存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<TableData> InsertBomDataByExcel(AddBomExcel input)
    {
        try
        {
            List<BomExcelDto> itemList = !string.IsNullOrEmpty(input.Items) ? JsonConvert.DeserializeObject<List<BomExcelDto>>(input.Items) : new List<BomExcelDto>();
            //上个的父项物料编码
            var lastProdMatCode = "";
            string newId = "";
            //上一个成品编码
            foreach (var item in itemList)
            {
                //获取到最新成品编号
                if (lastProdMatCode != item.ItemMCode)
                {
                    var strDate = "CP" + DateTime.Now.ToString("yyyyMM");
                    var maxId = await _db.Queryable<Items>().Where(x => x.FGCode.Contains(strDate)).OrderByDescending(x => x.FGCode).Select(x => x.FGCode).FirstAsync();
                    if (string.IsNullOrEmpty(maxId))
                    {
                        newId = strDate + "01";
                    }
                    else
                    {
                        string fgCodeEnd = maxId.Substring(maxId.Length - 2); // 获取最后两位
                        if (int.TryParse(fgCodeEnd, out int lastTwoDigits))
                        {
                            int newFGCode = lastTwoDigits + 1; // 加 1
                            newId = strDate + newFGCode.ToString().PadLeft(2, '0'); // 确保两位数
                        }
                    }
                    //新增成品编号
                    var entity = new Items();
                    entity.Status = "new";
                    entity.FGCode = newId;
                    entity.SeriesNo = item.SeriesNo;
                    entity.StyleNo = item.StyleNo;
                    entity.ProdMatCode = item.ItemMCode;
                    await _tucfpRepository.InsertReturnIdAsync(entity);
                    lastProdMatCode = item.ItemMCode;
                }
                //获取到料件信息
                var tcm = await _tcmRepository.GetFirstAsync(x => x.MCode == item.MCode);
                if (tcm == null)
                {
                    throw new UserFriendlyException(L["Abnormal"]);
                }
                if (tcm.Status != "release")
                {
                    tcm.Status = "release";
                    tcm.Reviewer = _currentUser.GetUsername();
                    tcm.TimeReviewed = DateTime.Now;
                    await _tcmRepository.UpdateAsync(tcm);
                }

                //新增单耗
                var tech = new TechConsumption();
                tech.FGCode = newId;
                tech.SpecModel = tcm.SpecModel;
                tech.MName = item.MName;
                tech.IntCode = item.IntCode;
                tech.MCode = item.MCode;
                tech.Casing = item.Casing;
                tech.OriginalUnit = item.OriginalUnit;
                tech.EffectiveUsageRate = item.EffectiveUsageRate;
                tech.MopLength = item.MopLength;
                await _techConsumptionRepository.InsertReturnIdAsync(tech);
            }
            return new TableData { Data = L["UpdateSuccessful"] };
        }
        catch (Exception exc)
        {
            throw new UserFriendlyException(exc.Message);
        }
    }

    /// <summary>
    /// 更新单耗表数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> UpdateTechConsumption(AddTechConsumptionInput input)
    {
        if (input.Id == null)
        {
            throw new UserFriendlyException(L["Abnormal"]);
        }
        var techConsumptionInfo = await _techConsumptionRepository.GetByIdAsync(input.Id);
        if (techConsumptionInfo == null)
        {
            throw new UserFriendlyException(L["Abnormal"]);
        }
        var tucfpInfo = await _tucfpRepository.GetFirstAsync(x => x.FGCode == techConsumptionInfo.FGCode);
        if (tucfpInfo != null && tucfpInfo.Status != "new" && tucfpInfo.Status != "cnacel")
        {
            throw new UserFriendlyException(L["StatusErro3"]);
        }
        techConsumptionInfo.Casing = input.Casing;
        techConsumptionInfo.MopLength = input.MopLength;
        techConsumptionInfo.OriginalUnit = input.OriginalUnit;
        techConsumptionInfo.LossRate = input.LossRate;
        techConsumptionInfo.UnitConsumption = input.UnitConsumption;
        techConsumptionInfo.CusProvidedKit = input.CusProvidedKit;
        techConsumptionInfo.CusProvidedMopClothLength = input.CusProvidedMopClothLength;
        techConsumptionInfo.CusProvidUnitConsumption = input.CusProvidUnitConsumption;
        techConsumptionInfo.FullPageSets = input.FullPageSets;
        techConsumptionInfo.EffectiveUsageRate = input.EffectiveUsageRate;
        await _techConsumptionRepository.UpdateAsync(techConsumptionInfo);
        return new TableData
        {
            Message = L["SaveSuccessfule"]
        };
    }

    /// <summary>
    /// 下载导入模版
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<TableData> DownLoadExcelModel()
    {
        try
        {
            // 创建文件流并使用 using 语句自动管理资源
            using (var fileStream = new MemoryStream())
            {
                using (var package = new ExcelPackage(fileStream))
                {
                    // 创建工作表
                    var worksheet = package.Workbook.Worksheets.Add("导入料件信息");

                    // 设置列头
                    var headers = new string[]
                    {
                    "原商品料号(必填)",
                    "原材料名称(必填)",
                    "内部编号",
                    "规格型号",
                    "取套",
                    "拖布长度",
                    "原始单耗",
                    "损耗率",
                    "单耗",
                    "客供取套",
                    "客供拖布长度",
                    "客供单耗",
                    "整版套数",
                    "有效使用率"
                    };

                    // 设置列头
                    for (int i = 0; i < headers.Length; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = headers[i];
                    }

                    // 自动调整列宽
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // 保存文件内容
                    package.Save();
                }

                fileStream.Position = 0;  // 重置流位置
                var fileData = fileStream.ToArray(); ;
                // 返回 TableData
                return new TableData
                {
                    Data = new
                    {
                        FileContent = fileData, // 返回字节数组
                        FileName = "料件导入模板.xlsx",
                        MimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    }
                };
            }
        }
        catch (Exception ex)
        {
            // 记录异常并返回错误信息
            Console.Error.WriteLine($"Error generating Excel template: {ex.Message}");
            return new TableData
            {
                Code = 500,
                Message = "Failed to generate Excel template: " + ex.Message
            };
        }
    }
}