// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace @NameSpace@.Input;

/// <summary>
/// @ClassName@表输入模型
/// </summary>
public class @ClassName@Input : PaginationParams
{
    /// <summary>
    /// ID
    /// </summary>
    public long? Id { get; set; }
@props@
}
