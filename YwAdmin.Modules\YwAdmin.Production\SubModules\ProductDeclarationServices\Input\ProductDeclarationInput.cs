﻿// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YwAdmin.Production.SubModules.ProductDeclarationServices.Input;

/// <summary>
/// 商品申报要素表Input
/// </summary>
public class ProductDeclarationInput
{
    /// <summary>
    /// ID
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string? HSCode { get; set; }

    /// <summary>
    /// 其他
    /// </summary>
    public string? Items { get; set; }

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool? IsMust { get; set; }

    /// <summary>
    /// 要素编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 编码内容
    /// </summary>
    public string CodeContent { get; set; }

    /// <summary>
    /// 原材料料号
    /// </summary>
    public string MCode { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string SpecModel { get; set; }
}