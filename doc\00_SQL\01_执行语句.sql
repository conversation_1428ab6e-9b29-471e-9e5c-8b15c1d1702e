CREATE TABLE Purchase_Order (
    [ID] bigint  NOT NULL,
    OrderNo nvarchar(50) PRIMARY KEY,      -- 采购订单号（业务编号）
    OrderDate DATETIME NOT NULL,            -- 下单日期
    OrderType nvarchar(20) NOT NULL,         -- 'Import', 'Domestic'
    OrderStatus nvarchar(20) NOT NULL,         -- 'Created', 'Confirmed', 'Received'
    PoNo nvarchar(50),                  -- 合同编号或采购单参考号
    DeclarationNo nvarchar(50),                  -- 报关单号
    SupplierNo nvarchar(100) NOT NULL,        -- 供应商名称
    ReceiptDate DATETIME,                      -- 实际入库时间
	[CREATEUSERID] bigint  NOT NULL,
	[CREATETIME] datetime  NOT NULL,
	[UPDATATEUSERID] bigint  NULL,
	[UPDATETIME] datetime  NULL,
);
GO
ALTER TABLE [dbo].[Purchase_Order] SET (LOCK_ESCALATION = TABLE)
GO
EXEC sp_addextendedproperty 'MS_Description', N'订单号','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'OrderNo'
GO
EXEC sp_addextendedproperty 'MS_Description', N'订单时间','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'OrderDate'
GO
EXEC sp_addextendedproperty 'MS_Description', N'订单类型','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'OrderType'
GO
EXEC sp_addextendedproperty 'MS_Description', N'订单状态','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'OrderStatus'
GO
EXEC sp_addextendedproperty 'MS_Description', N'采购订单号','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'PoNo'
GO
EXEC sp_addextendedproperty 'MS_Description', N'报关单号','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'DeclarationNo'
GO
EXEC sp_addextendedproperty 'MS_Description', N'供应商','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'SupplierNo'
GO
EXEC sp_addextendedproperty 'MS_Description', N'入库时间','SCHEMA', N'dbo','TABLE', N'Purchase_Order','COLUMN', N'ReceiptDate'
GO
ALTER TABLE [dbo].[Purchase_Order] ADD CONSTRAINT [PK_Purchase_Order] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

CREATE TABLE Purchase_Order_Detail (
    ID   bigint   NOT NULL,
    OrderNo             VARCHAR(50) NOT NULL,        -- 外键，关联PO表
    Seq           INT NOT NULL,                -- 序号
    MCode         VARCHAR(50),                 -- 原材料款号
    ItemCode             VARCHAR(50) NOT NULL,        -- 商品编号
    ItemName             VARCHAR(100),                -- 商品名称
    SpecModel        VARCHAR(100),                -- 规格型号
    Unit                 VARCHAR(20),                 -- 单位
    Qty             DECIMAL(18,8) NOT NULL,      -- 数量
    PackageCount         INT,                         -- 件数
    GrossWeight          DECIMAL(18,8),               -- 毛重
    NetWeight            DECIMAL(18,8),               -- 净重
    Volume               DECIMAL(18,8),               -- 体积（立方米）
    UnitPrice            DECIMAL(18,8),               -- 单价
    Amount               DECIMAL(18,8),               -- 金额
    Currency             VARCHAR(10),                 -- 币种
		[CREATEUSERID] bigint  NOT NULL,
		[CREATETIME] datetime  NOT NULL,
		[UPDATATEUSERID] bigint  NULL,
		[UPDATETIME] datetime  NULL,
);
GO
ALTER TABLE [dbo].[Purchase_Order_Detail] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty 'MS_Description', N'订单号','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'OrderNo'
GO

EXEC sp_addextendedproperty 'MS_Description', N'序号','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'Seq'
GO

EXEC sp_addextendedproperty 'MS_Description', N'原材料料号','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'MCode'
GO

EXEC sp_addextendedproperty 'MS_Description', N'商品编号','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'ItemCode'
GO

EXEC sp_addextendedproperty 'MS_Description', N'商品名称','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'ItemName'
GO

EXEC sp_addextendedproperty 'MS_Description', N'规格型号','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'SpecModel'

GO
EXEC sp_addextendedproperty 'MS_Description', N'规格型号','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'Unit'
GO

EXEC sp_addextendedproperty 'MS_Description', N'数量','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'Qty'
GO

EXEC sp_addextendedproperty 'MS_Description', N'件数','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'PackageCount'
GO

EXEC sp_addextendedproperty 'MS_Description', N'毛重','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'GrossWeight'
GO

EXEC sp_addextendedproperty 'MS_Description', N'净重','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'NetWeight'
GO

EXEC sp_addextendedproperty 'MS_Description', N'体积','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'Volume'
GO

EXEC sp_addextendedproperty 'MS_Description', N'单价','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'UnitPrice'
GO

EXEC sp_addextendedproperty 'MS_Description', N'金额','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'Amount'
GO

EXEC sp_addextendedproperty 'MS_Description', N'币种','SCHEMA', N'dbo','TABLE', N'Purchase_Order_Detail','COLUMN', N'Currency'
GO

ALTER TABLE [dbo].[Purchase_Order_Detail] ADD CONSTRAINT [PK_Purchase_Order_Detail] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

--2025-06-24
--删除贸易方式
ALTER TABLE [dbo].[Material] DROP COLUMN [TradeMode]

--删除要素分类
ALTER TABLE [dbo].[Product_Declaration] DROP COLUMN [Category]

--申报要素表新增字段：必填、要素编码、要素内容
alter table Product_Declaration add IsMust bit null
GO
EXEC sp_addextendedproperty 'MS_Description', N'是否必填','SCHEMA', N'dbo','TABLE', N'Product_Declaration','COLUMN', N'IsMust'
Go
alter table Product_Declaration add Code nvarchar(30) null
GO
EXEC sp_addextendedproperty 'MS_Description', N'要素编码','SCHEMA', N'dbo','TABLE', N'Product_Declaration','COLUMN', N'Code'
Go
alter table Product_Declaration add CodeContent nvarchar(150) null
GO
EXEC sp_addextendedproperty 'MS_Description', N'要素内容','SCHEMA', N'dbo','TABLE', N'Product_Declaration','COLUMN', N'CodeContent'
Go


--2025-07-12
--新增进口报关单表
CREATE TABLE Import_CustomsDeclaration (
    Id BIGINT PRIMARY KEY IDENTITY(1,1) not null, -- 主键ID
	CustomsNo NVARCHAR(50) not null, -- 报关单号
    OrderNo NVARCHAR(50) not null, -- 订单号
    
    -- 出口公司信息
    ExportCompanyCode NVARCHAR(50), -- 出口公司代码
    ExportCompanyName NVARCHAR(100), -- 出口公司名称
	ExportTaxCode NVARCHAR(50), -- 出口公司税号
    ExportPostalCode NVARCHAR(20), -- 出口公司邮政编码
    ExportAddress NVARCHAR(500), -- 出口公司地址
    ExportCountryCode NVARCHAR(10), -- 出口公司国家代码
    
    -- 进口公司信息
    ImportCompanyCode NVARCHAR(50), -- 进口公司代码
    ImportCompanyName NVARCHAR(200), -- 进口公司名称
	ImportTaxCode NVARCHAR(50), -- 进口公司名称
    ImportPostalCode NVARCHAR(20), -- 进口公司邮政编码
    ImportAddress NVARCHAR(500), -- 进口公司地址
    ImportPhone NVARCHAR(50), -- 进口公司电话号码
    ImportContactPerson NVARCHAR(100), -- 进口公司联系人名称
    ImportContactPhone NVARCHAR(50), -- 进口公司联系人电话号码
    ImportEmail NVARCHAR(100), -- 进口公司邮件
    ImportTypeCode NVARCHAR(10), -- 进口公司类型代码(1:生产企业,2:贸易公司,3:其他)
    ImportCustomsAuthority NVARCHAR(100), -- 进口公司海关当局
    ImportGoodsClassificationCode NVARCHAR(50), -- 进口公司货物分类代码
    ImportPersonOrgClassification NVARCHAR(20), -- 进口公司个人/组织分类(person/organization)
    ImportDeclarationDeptCode NVARCHAR(50), -- 进口公司申报单处理部门代码
    ImportTransportModeCode NVARCHAR(20), -- 进口公司运输方式代码
    
    -- 提单信息
    BillNo NVARCHAR(100), -- 提单号
    ATD DATE, -- 开航日期
    PackageCount INT, -- 提单件数
    GrossWeight DECIMAL(18,8), -- 提单货物毛重(kg)
    WarehouseLocationCode NVARCHAR(50), -- 提单预期仓库位置代码
    TransportMode NVARCHAR(20), -- 提单运输方式(sea/air/land/rail)
    ATA DATE, -- 提单到货日期
    DischargePort NVARCHAR(100), -- 提单卸货港
    LoadingPort NVARCHAR(100), -- 提单装货港
    ContainerCount INT, -- 提单集装箱数量
    
    -- 许可证信息 
	LicenseContractNo NVARCHAR(50), --加工合同单号（可为空）
	LicenseContractDate Date, -- 合同时间
	LicenseExpiryDate Date, -- 合同到期日
	LicenseImportNo NVARCHAR(50), -- 进口许可证
    
    -- 发票信息 (预留字段，根据实际需求补充)
	InvoiceType NVARCHAR(50) NULL,              -- 发票分类
    InvoiceNo NVARCHAR(50) NULL,            -- 发票号码
    InvoiceDate DATE NULL,                      -- 开具日期
    PaymentMethod NVARCHAR(50) NULL,            -- 付款方式
    InvoiceTypeCode NVARCHAR(50) NULL,          -- 发票分类代码
    InvoiceAmount DECIMAL(18, 8) NULL,          -- 发票金额
    DeliveryTerms NVARCHAR(100) NULL,           -- 交货条款
    Currency NVARCHAR(10) NULL,                 -- 货币单位
    CustomsValueCode NVARCHAR(50) NULL,         -- 价值申报的分类代码
    Freight DECIMAL(18, 8) NULL,                -- 运费
    Insurance DECIMAL(18, 8) NULL,            -- 保险费
    
    -- 税务信息 (预留字段，根据实际需求补充)
    TaxDueDateCode NVARCHAR(50), --确定纳税截止日期代码
	
    -- 系统字段
	[CREATEUSERID] bigint  NOT NULL,
	[CREATETIME] datetime  NOT NULL,
	[UPDATATEUSERID] bigint  NULL,
	[UPDATETIME] datetime  NULL,
);

GO

EXEC sp_addextendedproperty
'MS_Description', N'进口报关单表',
'SCHEMA', N'dbo',
'TABLE', N'Import_CustomsDeclaration'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'报关单号','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'CustomsNo'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'订单号','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'OrderNo'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'出口公司代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ExportCompanyCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'出口公司税号','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ExportTaxCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'出口公司名称','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ExportCompanyName'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'出口公司邮政编码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ExportPostalCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'出口公司地址','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ExportAddress'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'出口公司国家代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ExportCountryCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportCompanyCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司名称','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportCompanyName'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司税号','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportTaxCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司邮政编码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportPostalCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司地址','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportAddress'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司电话号码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportPhone'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司联系人名称','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportContactPerson'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司联系人电话号码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportContactPhone'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司邮件','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportEmail'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司类型代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportTypeCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司海关当局','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportCustomsAuthority'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司货物分类代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportGoodsClassificationCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司个人/组织分类','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportPersonOrgClassification'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司申报单处理部门代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportDeclarationDeptCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'进口公司运输方式代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ImportTransportModeCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单号','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'BillNo'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'开航日期','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ATD'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单件数','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'PackageCount'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单货物毛重','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'GrossWeight'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单预期仓库位置代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'WarehouseLocationCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单运输方式','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'TransportMode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单到货日期','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ATA'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单卸货港','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'DischargePort'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单装货港','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'LoadingPort'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'提单集装箱数量','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'ContainerCount'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'许可证信息加工合同单号','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'LicenseContractNo'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'许可证信息合同时间','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'LicenseContractDate'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'许可证信息合同到期日','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'LicenseExpiryDate'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'许可证信息进口许可证','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'LicenseImportNo'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'发票分类','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'InvoiceType'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'发票号码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'InvoiceNo'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'开具日期','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'InvoiceDate'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'付款方式','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'PaymentMethod'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'发票分类代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'InvoiceTypeCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'发票金额','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'InvoiceAmount'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'交货条款','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'DeliveryTerms'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'货币单位','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'Currency'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'价值申报的分类代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'CustomsValueCode'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'运费','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'Freight'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'保险费','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'Insurance'
GO

GO
EXEC sp_addextendedproperty 'MS_Description', N'确定纳税截止日期代码','SCHEMA', N'dbo','TABLE', N'Import_CustomsDeclaration','COLUMN', N'TaxDueDateCode'
GO


--添加公司信息表

CREATE TABLE base_CompanyInfo (
    Id INT IDENTITY(1,1) PRIMARY KEY,         -- 主键
    CompanyCode NVARCHAR(50) NOT NULL,        -- 公司代码（新增，用于唯一识别公司）
    TaxCode NVARCHAR(50) NOT NULL,            -- 公司税号
    Name NVARCHAR(200) NOT NULL,              -- 公司名称
    PostalCode NVARCHAR(20),                  -- 邮政编码
    Address NVARCHAR(500),                    -- 地址
    Phone NVARCHAR(50),                       -- 电话号码
    ContactName NVARCHAR(100),                -- 联系人名称
    ContactPhone NVARCHAR(50),                -- 联系人电话号码
    Email NVARCHAR(100),                      -- 邮件
    CountryCode NVARCHAR(10),                 -- 国家代码
    TypeCode NVARCHAR(10),                    -- 类型代码
    CustomsAuthority NVARCHAR(100),           -- 海关当局
    GoodsClassificationCode NVARCHAR(50),     -- 货物分类代码
    PersonOrgType NVARCHAR(20),               -- 个人/组织分类
    DeclarationDeptCode NVARCHAR(50),         -- 申报单处理部门代码
    TransportModeCode NVARCHAR(20),            -- 运输方式代码
	-- 系统字段
	[CREATEUSERID] bigint  NOT NULL,
	[CREATETIME] datetime  NOT NULL,
	[UPDATATEUSERID] bigint  NULL,
	[UPDATETIME] datetime  NULL,
);

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'公司信息表',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'base_CompanyInfo';
	
-- 公司代码
EXEC sp_addextendedproperty 
    'MS_Description', N'公司代码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'CompanyCode';

-- 公司税号
EXEC sp_addextendedproperty 
    'MS_Description', N'公司税号',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'TaxCode';

-- 公司名称
EXEC sp_addextendedproperty 
    'MS_Description', N'公司名称',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'Name';

-- 邮政编码
EXEC sp_addextendedproperty 
    'MS_Description', N'邮政编码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'PostalCode';

-- 地址
EXEC sp_addextendedproperty 
    'MS_Description', N'地址',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'Address';

-- 电话号码
EXEC sp_addextendedproperty 
    'MS_Description', N'电话号码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'Phone';

-- 联系人名称
EXEC sp_addextendedproperty 
    'MS_Description', N'联系人名称',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'ContactName';

-- 联系人电话号码
EXEC sp_addextendedproperty 
    'MS_Description', N'联系人电话号码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'ContactPhone';

-- 邮件
EXEC sp_addextendedproperty 
    'MS_Description', N'邮件',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'Email';

-- 国家代码
EXEC sp_addextendedproperty 
    'MS_Description', N'国家代码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'CountryCode';

-- 类型代码
EXEC sp_addextendedproperty 
    'MS_Description', N'类型代码（1生产企业，2贸易公司，3其他）',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'TypeCode';

-- 海关当局
EXEC sp_addextendedproperty 
    'MS_Description', N'海关当局',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'CustomsAuthority';

-- 货物分类代码
EXEC sp_addextendedproperty 
    'MS_Description', N'货物分类代码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'GoodsClassificationCode';

-- 个人/组织分类
EXEC sp_addextendedproperty 
    'MS_Description', N'个人/组织分类（person/organization）',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'PersonOrgType';

-- 申报单处理部门代码
EXEC sp_addextendedproperty 
    'MS_Description', N'申报单处理部门代码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'DeclarationDeptCode';

-- 运输方式代码
EXEC sp_addextendedproperty 
    'MS_Description', N'运输方式代码',
    'SCHEMA', N'dbo', 'TABLE', N'base_CompanyInfo', 'COLUMN', N'TransportModeCode';
	
	
	
-- 2025-08-03 添加
-- 入库表
-- 入库明细表

CREATE TABLE Inv_Inbound (
    [ID] bigint  NOT NULL,
    ReceiptNo nvarchar(50) PRIMARY KEY,      -- 入库单号
    ReceiptDate DATETIME,                      -- 入库时间
    InvStatus nvarchar(20) NOT NULL,         -- 'Import', 'Domestic'
    SupplierNo nvarchar(100) NOT NULL,        -- 供应商代码
	InvType nvarchar(20) not null, -- "采购：purchase"
	OrderNo nvarchar(50), -- 订单号
	WarehouseCode nvarchar(50), -- 仓库代码
	[CREATEUSERID] bigint  NOT NULL,
	[CREATETIME] datetime  NOT NULL,
	[UPDATATEUSERID] bigint  NULL,
	[UPDATETIME] datetime  NULL,
);
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'入库表',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'Inv_Inbound';
	
EXEC sp_addextendedproperty
'MS_Description', N'入库单号',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound',
'COLUMN', N'ReceiptNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'入库时间',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound',
'COLUMN', N'ReceiptDate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'类型',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound',
'COLUMN', N'InvStatus'
GO

EXEC sp_addextendedproperty
'MS_Description', N'供应商代码',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound',
'COLUMN', N'SupplierNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'订单号',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound',
'COLUMN', N'OrderNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'仓库代码',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound',
'COLUMN', N'WarehouseCode'
GO


CREATE TABLE [dbo].[Inv_Inbound_Detail] (
  [ID] bigint  NOT NULL,
  [ReceiptNo] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Seq] int  NOT NULL,
  [MCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [ItemCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [ItemName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [SpecModel] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [Unit] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [Qty] decimal(18,8)  NULL,
  [PackageCount] int  NULL,
  [GrossWeight] decimal(18,8)  NULL,
  [NetWeight] decimal(18,8)  NULL,
  [Volume] decimal(18,8)  NULL,
  [UnitPrice] decimal(18,8)  NULL,
  [Amount] decimal(18,8)  NULL,
  [Currency] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NULL,
  [CREATEUSERID] bigint  NOT NULL,
  [CREATETIME] datetime  NOT NULL,
  [UPDATATEUSERID] bigint  NULL,
  [UPDATETIME] datetime  NULL
)
GO

ALTER TABLE [dbo].[Inv_Inbound_Detail] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'入库单号',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'OrderNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'序号',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'Seq'
GO

EXEC sp_addextendedproperty
'MS_Description', N'原材料料号',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'MCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'商品编号',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'ItemCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'商品名称',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'ItemName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格型号',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'SpecModel'
GO

EXEC sp_addextendedproperty
'MS_Description', N'单位',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'Unit'
GO

EXEC sp_addextendedproperty
'MS_Description', N'数量',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'Qty'
GO

EXEC sp_addextendedproperty
'MS_Description', N'件数',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'PackageCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'毛重',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'GrossWeight'
GO

EXEC sp_addextendedproperty
'MS_Description', N'净重',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'NetWeight'
GO

EXEC sp_addextendedproperty
'MS_Description', N'体积',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'Volume'
GO

EXEC sp_addextendedproperty
'MS_Description', N'单价',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'UnitPrice'
GO

EXEC sp_addextendedproperty
'MS_Description', N'金额',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'Amount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'币种',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail',
'COLUMN', N'Currency'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采购订单明细表',
'SCHEMA', N'dbo',
'TABLE', N'Inv_Inbound_Detail'
GO

ALTER TABLE [dbo].[Inv_Inbound_Detail] ADD CONSTRAINT [PK_Inv_Inbound_Detail] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- 2025-08-12
-- 添加仓库表
CREATE TABLE dbo.Warehouse (
    Id int IDENTITY(1,1) PRIMARY KEY,      -- 主键
    WarehouseCode NVARCHAR(20) NOT NULL,               -- 仓库代码
    WarehouseName NVARCHAR(100) NOT NULL,              -- 仓库名称
    TypeCode NVARCHAR(20) NOT NULL,           -- 仓库类别（关联数据字典）
    Address NVARCHAR(200) NULL,               -- 仓库地址
    ContactName NVARCHAR(50) NULL,            -- 负责人
    ContactPhone NVARCHAR(20) NULL,           -- 联系电话
    Capacity DECIMAL(18,2) NULL,               -- 仓库容量（可选）
    Status TINYINT NOT NULL DEFAULT 1,        -- 状态(1=启用,0=停用)
		[CREATEUSERID] bigint  NOT NULL,
    [CREATETIME] datetime  NOT NULL,
    [UPDATATEUSERID] bigint  NULL,
    [UPDATETIME] datetime  NULL
);


-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'仓库表',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse';
GO

-- 字段注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'主键ID',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'Id';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'仓库代码',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'WarehouseCode';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'仓库名称',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'WarehouseName';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'仓库类别代码（关联数据字典）',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'TypeCode';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'仓库地址',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'Address';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'负责人',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'ContactName';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'负责人联系电话',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'ContactPhone';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'仓库容量',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'Capacity';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'状态（1=启用，0=停用）',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'Warehouse',
    @level2type = N'COLUMN', @level2name = 'Status';
GO


GO

