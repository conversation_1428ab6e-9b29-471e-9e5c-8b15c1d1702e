﻿using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using YwAdmin.Application.AuthServices.Dtos;
using YwAdmin.Application.Localization;

using YwAdmin.Core.DataEncryption.Encryptions;

using YwAdmin.Multiplex.AdminUser;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts.Models;
using YwAdmin.Multiplex.Contracts.IAdminUser.OAuth2;
using Volo.Abp.Localization;
using System.Runtime.CompilerServices;
using YwAdmin.Multiplex.IAdminUser.OAuth2;


namespace YwAdmin.Application.AuthServices;
/// <summary>
/// 用户授权服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
public class AuthService(IOAuth2UserManager oAuth2UserManager, IHubContext<AuthorizationHub, IAuthorizationClient> hubContext, IConfiguration configuration, IAdminToken adminToken, IHttpContextAccessor httpContextAccessor, ISqlSugarClient db, ICurrentUser currentUser, IDbTokenService dbTokenService
    , IStringLocalizer<ApplicationResource> localizer) : ApplicationServiceBase
{
    /// <summary>
    /// oAuth2UserManager
    /// </summary>
    private readonly IOAuth2UserManager _oAuth2UserManager = oAuth2UserManager;
    /// <summary>
    /// hubContext
    /// </summary>
    private readonly IHubContext<AuthorizationHub, IAuthorizationClient> _hubContext = hubContext;
    /// <summary>
    /// configuration
    /// </summary>
    private readonly IConfiguration _configuration = configuration;
    /// <summary>
    /// IAdminToken
    /// </summary>
    private readonly IAdminToken _adminToken = adminToken;
    /// <summary>
    /// db
    /// </summary>
    private readonly ISqlSugarClient _db = db;
    /// <summary>
    /// IHttpContextAccessor
    /// </summary>
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    /// <summary>
    /// 当前用户
    /// </summary>
    private readonly ICurrentUser _currentUser = currentUser;

    /// <summary>
    /// 数据库Token服务
    /// </summary>
    private readonly IDbTokenService _dbTokenService = dbTokenService;

    private readonly IStringLocalizer<ApplicationResource> _localizer = localizer;


    [AllowAnonymous]
    public async Task<string> demotest()
    {
        var str = _localizer["Hello"];
        var str2 = L["Hello"];
        return str;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [AllowAnonymous]
    public async Task<TableData> LoginAsync([Required] LoginInput input)
    {
        try
        {
            // 判断用户名或密码是否正确
            var password = MD5Encryption.Encrypt(input.Password);
            var user = await _db.Queryable<UserEntity>().FirstAsync(u => u.Account.Equals(input.Account) && u.Password.Equals(password)) ?? throw PersistdValidateException.Message("用户名不存在或用户名密码错误！");
            if (user == null)
            {
                throw new UserFriendlyException("用户验证失败！");
            }
            if (user.Status != (int)UserStatusEnum.Normal) throw PersistdValidateException.Message("帐号状态异常，请联系管理员");

            // 获取设备信息
            var httpContext = _httpContextAccessor.HttpContext;
            var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
            var ipAddress = YwAdmin.Core.Utils.IpHelper.GetClientIp(httpContext);
            var deviceInfo = YwAdmin.Core.Utils.IpHelper.GetDeviceInfoHash(httpContext);

            // 使用数据库Token服务创建Token
            var tokenEntity = await _dbTokenService.CreateTokenAsync(
                userId: user.Id,
                deviceInfo: deviceInfo,
                ipAddress: ipAddress,
                userAgent: userAgent
            );

            // 映射结果
            var output = user.Adapt<LoginOutput>();
            output.AccessToken = tokenEntity.TokenValue;
            output.RefreshToken = tokenEntity.RefreshToken;
            output.Username = user.Account;
            output.RealName = user.Name;
            var roleIdList = (from s in db.Queryable<UserRoleEntity>()
                              where s.UserId == user.Id
                              select s.RoleId).ToList();
            output.Roles = (from s in db.Queryable<Role>()
                            where roleIdList.Contains(s.Id)
                            select s.RoleName).ToList();

            // 构造返回结果
            var result = new TableData
            {
                Code = 0,
                Message = "登录成功！",
                Data = output,
            };
            return result;
        }
        catch (Exception exc)
        {
            var result = new TableData
            {
                Code = 401,
                Message = exc.Message,
            };
            return result;
        }



    }

    /// <summary>
    /// 刷新token
    /// </summary>
    /// <param name="refreshToken">刷新Token</param>
    /// <returns>刷新结果</returns>
    [AllowAnonymous]
    public async Task<TableData> RefreshToken([Required] string refreshToken)
    {
        try
        {
            // 使用数据库Token服务刷新Token
            var tokenEntity = await _dbTokenService.RefreshTokenAsync(refreshToken);

            if (tokenEntity == null)
            {
                return new TableData
                {
                    Code = 401,
                    Message = "刷新Token失败，请重新登录！"
                };
            }

            // 获取用户信息
            var user = await _db.Queryable<UserEntity>().FirstAsync(u => u.Id == tokenEntity.UserId);

            if (user == null || user.Status != (int)UserStatusEnum.Normal)
            {
                return new TableData
                {
                    Code = 401,
                    Message = "用户状态异常，请重新登录！"
                };
            }

            // 构造返回结果
            var output = new LoginOutput
            {
                AccessToken = tokenEntity.TokenValue,
                RefreshToken = tokenEntity.RefreshToken,
                Username = user.Account,
                RealName = user.Name,
                Roles = new List<string> { "super" }
            };

            return new TableData
            {
                Code = 0,
                Message = "刷新Token成功！",
                Data = output
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 401,
                Message = exc.Message
            };
        }
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <returns>登出结果</returns>
    [HttpPost]
    public async Task<TableData> Logout()
    {
        try
        {
            // 获取当前Token
            var token = _httpContextAccessor.HttpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

            if (!string.IsNullOrEmpty(token))
            {
                // 使用数据库Token服务撤销Token
                await _dbTokenService.RevokeTokenAsync(token);
            }

            return new TableData
            {
                Code = 0,
                Message = "登出成功！"
            };
        }
        catch (Exception exc)
        {
            return new TableData
            {
                Code = 500,
                Message = $"登出失败：{exc.Message}"
            };
        }
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetUserInfo()
    {
        var token = _httpContextAccessor.HttpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (string.IsNullOrEmpty(token))
        {
            return new TableData
            {
                Code = 1,
                Message = "Token不能为空",
            };
        }

        // 解码 Token 并获取 UserId
        var userId = GetUserIdFromToken(token);
        if (userId == null)
        {
            return new TableData
            {
                Code = 1,
                Message = "无效的Token",
            };
        }

        // 根据 UserId 查询用户信息
        var user = await _db.Queryable<UserEntity>().FirstAsync(x => x.Id == userId);
        if (user == null)
        {
            return new TableData
            {
                Code = 1,
                Message = "用户未找到",
            };
        }

        var output = user.Adapt<GetVbenUserInfoOutput>();
        output.RealName = user.Name;
        output.Username = user.Account;
        output.TelePhone = user.Telephone;
        output.Email = user.Email;
        var roleIdList = (from s in db.Queryable<UserRoleEntity>()
                          where s.UserId == userId
                          select s.RoleId).ToList();
        output.Roles = (from s in db.Queryable<Role>()
                        where roleIdList.Contains(s.Id)
                        select s.RoleName).ToList();
        output.DeptName = _db.Queryable<OrganizationEntity>().First(x => x.Id == user.OrganizationId)?.Name;
        output.Avatar = user.Avatar;
        output.UserId = user.Id;
        var result = new TableData
        {
            Code = 0,
            Message = "查询成功",
            Data = output,
        };
        return result;

    }

    /// <summary>
    /// 解码Token
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    private long? GetUserIdFromToken(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadToken(token) as JwtSecurityToken;

            // 提取 UserId，假设 Token 中有一个名为 "userId" 的 claim
            var userIdString = jsonToken?.Claims.FirstOrDefault(c => c.Type == "userid")?.Value;

            // 尝试将 userId 转换为 long 类型
            if (long.TryParse(userIdString, out long userId))
            {
                return userId;
            }
            return null;
        }
        catch
        {
            // 解析失败或者 Token 无效时返回 null
            return null;
        }
    }
    [HttpPost]
    public async Task<TableData> GetCodes()
    {

        //"AC_100100",
        //"AC_100110",
        //"AC_100120",
        //"AC_100010"

        try
        {
            //获取当前ID的所有角色
            var userId = _currentUser.Id;
            var roleIdList = (from s in db.Queryable<UserRoleEntity>()
                              where s.UserId == userId
                              select s.RoleId).ToList();
            var resultList = await _db.Queryable<RoleFunction>().Where(x => roleIdList.Contains(x.RoleId)).Select(x => x.PermCode).Distinct().ToListAsync();

            //    var data = new List<string>
            //{
            //    "sysManagement",
            //    "user.edit",
            //    "user.delete",
            //    "user.home",
            //    "AC_100010"
            //};

            return new TableData { Data = resultList };
        }
        catch (Exception exc)
        {
            return new TableData { Code = 500, Message = exc.Message };
        }
    }

}
