﻿// Copyright © 2024-present wzw

using SqlSugar.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace YwAdmin.Warehouse.Utility.Helper;

/// <summary>
/// 帮助类
/// </summary>
public static class Helper
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <returns></returns>
    public static int ObjToInt(this object thisValue)
    {
        int reval = 0;
        if (thisValue == null) return 0;
        if (thisValue != null && thisValue != DBNull.Value && int.TryParse(thisValue.ToString(), out reval))
        {
            return reval;
        }
        return reval;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <param name="errorValue"></param>
    /// <returns></returns>
    public static int ObjToInt(this object thisValue, int errorValue)
    {
        int reval = 0;
        if (thisValue != null && thisValue != DBNull.Value && int.TryParse(thisValue.ToString(), out reval))
        {
            return reval;
        }
        return errorValue;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <returns></returns>
    public static long ObjToLong(this object thisValue)
    {
        long reval = 0L;
        if (thisValue != null && thisValue != DBNull.Value && long.TryParse(thisValue.ToString(), out reval))
        {
            return reval;
        }
        return reval;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <param name="errorValue"></param>
    /// <returns></returns>
    public static string ObjToString(this object thisValue, string errorValue)
    {
        if (thisValue != null) return thisValue.ToString().Trim();
        return errorValue;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <returns></returns>
    public static Decimal ObjToDecimal(this object thisValue)
    {
        Decimal reval = 0;
        if (thisValue != null && thisValue != DBNull.Value && decimal.TryParse(thisValue.ToString().Trim(), out reval))
        {
            return reval;
        }
        return 0;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <param name="errorValue"></param>
    /// <returns></returns>
    public static Decimal ObjToDecimal(this object thisValue, decimal errorValue)
    {
        Decimal reval = 0;
        if (thisValue != null && thisValue != DBNull.Value && decimal.TryParse(thisValue.ToString(), out reval))
        {
            return reval;
        }
        return errorValue;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <returns></returns>
    public static DateTime ObjToDate(this object thisValue)
    {
        DateTime reval = DateTime.MinValue;
        if (thisValue != null && thisValue != DBNull.Value && DateTime.TryParse(thisValue.ToString(), out reval))
        {
            reval = Convert.ToDateTime(thisValue);
        }
        return reval;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <param name="errorValue"></param>
    /// <returns></returns>
    public static DateTime ObjToDate(this object thisValue, DateTime errorValue)
    {
        DateTime reval = DateTime.MinValue;
        if (thisValue != null && thisValue != DBNull.Value && DateTime.TryParse(thisValue.ToString(), out reval))
        {
            return reval;
        }
        return errorValue;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="thisValue"></param>
    /// <returns></returns>
    public static bool ObjToBool(this object thisValue)
    {
        bool reval = false;
        if (thisValue != null && thisValue != DBNull.Value && bool.TryParse(thisValue.ToString(), out reval))
        {
            return reval;
        }
        return reval;
    }


    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static Boolean IsEmpty<T>(this IEnumerable<T> source)
    {
        if (source == null)
            return true;
        return !source.Any();
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static Boolean IsEmpty<T>(this IList<T> source)
    {
        if (source == null)
            return true;
        return !source.Any();
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static Boolean IsEmpty<T>(this List<T> source)
    {
        if (source == null)
            return true;
        return !source.Any();
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static Boolean IsEmpty<T>(this Array source)
    {
        if (source == null)
            return true;
        return !(source.Length > 0);
    }
    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source">数组</param>
    /// <param name="comparedLength">相对长度，判断数组内长度是否满足要求</param>
    /// <typeparam name="T">类型</typeparam>
    /// <returns></returns>
    public static Boolean IsEmpty<T>(this Array source, int comparedLength)
    {
        if (source == null)
            return true;
        return !(source.Length == comparedLength);
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source">数组</param>
    /// <typeparam name="T">类型</typeparam>
    /// <returns></returns>
    public static Boolean IsEmpty<T>(this T source)
    {
        if (source == null)
            return true;
        return false;
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source">数组</param>
    /// <returns></returns>
    public static Boolean IsEmpty(this string source)
    {
        if (String.IsNullOrWhiteSpace(source))
            return true;
        return false;
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source">数组</param>
    /// <returns></returns>
    public static Boolean IsEmpty(this long? source)
    {
        if (source == null)
        {
            return true;
        }
        else
        {
            if (source.ObjToLong() <= 0)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source">数组</param>
    /// <returns></returns>
    public static Boolean IsEmpty(this long source)
    {
        if (source.ObjToLong() <= 0)
        {
            return true;
        }
        return false;
    }

    /// <summary>
    /// 验证数据是否存在
    /// </summary>
    /// <param name="source">数组</param>
    /// <returns></returns>
    public static Boolean IsEmpty(this int source)
    {
        if (source.ObjToInt() <= 0)
        {
            return true;
        }
        return false;
    }

}

