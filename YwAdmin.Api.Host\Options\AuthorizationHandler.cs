﻿using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Timing;
using YwAdmin.Multiplex.AdminUser;
using YwAdmin.Multiplex.Contracts.IAdminUser;

namespace YwAdmin.Api.Host.Options;

public class AuthorizationHandler : IAuthorizationHandler
{
    private readonly IHostEnvironment _hostEnvironment;
    private readonly ICurrentUser _currentUser;
    private readonly IAdminToken _adminToken;
    private readonly IClock _clock;
    private readonly IConfiguration _configuration;
    private readonly IDbTokenService _dbTokenService;
    private readonly ILogger<AuthorizationHandler> _logger;

    public AuthorizationHandler(
        IHostEnvironment hostEnvironment,
        ICurrentUser currentUser,
        IAdminToken adminToken,
        IClock clock,
        IConfiguration configuration,
        IDbTokenService dbTokenService,
        ILogger<AuthorizationHandler> logger)
    {
        _hostEnvironment = hostEnvironment;
        _currentUser = currentUser;
        _adminToken = adminToken;
        _clock = clock;
        _configuration = configuration;
        _dbTokenService = dbTokenService;
        _logger = logger;
    }

    public async Task HandleAsync(AuthorizationHandlerContext context)
    {
        if (context.Resource is HttpContext httpContext)
        {
            var isAuthenticated = httpContext.User.Identity?.IsAuthenticated ?? false;
            if (!isAuthenticated)
            {
                context.Fail();
                return;
            }
            //校验用户是否有接口权限
            if (_hostEnvironment.IsProduction())
            {
                //var endpoint = httpContext.GetEndpoint() as RouteEndpoint;
                //var pattern = endpoint?.RoutePattern;
                //var interfaces = await _currentUser.GetInterfacesAsync();
                //var isAllow = interfaces.Any(x => x.Path == pattern?.RawText && x.RequestMethod.Equals(httpContext.Request.Method, StringComparison.CurrentCultureIgnoreCase));
                //if (!isAllow)
                //{
                //    context.Fail();
                //    return;
                //}
            }
            // 获取JWT配置选项
            var jwtOptions = _configuration.GetRequiredSection("JwtOptions").Get<JwtOptions>() ?? new JwtOptions();

            // 获取当前Token
            var accessToken = httpContext.Request.Headers.Authorization.ToString().Replace("Bearer ", "");

            // 验证Token是否有效（包括黑名单检查和设备信息验证）
            if (!await _adminToken.ValidateTokenAsync(accessToken))
            {
                // 明确设置为认证失败（401）而不是授权失败（403）
                context.Fail(new AuthorizationFailureReason(this, "Token验证失败，需要重新登录"));

                // 设置响应状态码为401
                httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;

                return;
            }

            // 解析Token
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
            var jwtSecurityToken = jwtSecurityTokenHandler.ReadJwtToken(accessToken);
            var expires = jwtSecurityToken.ValidTo;

            // 检查是否需要自动登出（长时间不活跃）
            if (jwtOptions.EnableAutoLogout)
            {
                // 获取最后活动时间（如果有）
                var lastActivityClaim = jwtSecurityToken.Claims.FirstOrDefault(c => c.Type == "last_activity");
                if (lastActivityClaim != null && DateTime.TryParse(lastActivityClaim.Value, out var lastActivity))
                {
                    // 计算不活跃时间（分钟）
                    var inactiveMinutes = (_clock.Now.ToUniversalTime() - lastActivity.ToUniversalTime()).TotalMinutes;

                    // 如果不活跃时间超过配置的自动登出时间，则使Token失效
                    if (inactiveMinutes > jwtOptions.AutoLogoutMinutes)
                    {
                        await _adminToken.RevokeTokenAsync(accessToken);

                        // 明确设置为认证失败（401）而不是授权失败（403）
                        context.Fail(new AuthorizationFailureReason(this, "会话超时，需要重新登录"));

                        // 设置响应状态码为401
                        httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;

                        return;
                    }
                }
            }

            // 更新数据库中的最后活动时间
            await _dbTokenService.UpdateLastActivityAsync(accessToken);

            // 单token无感刷新
            var refreshMinutes = _adminToken.GetRefreshMinutes();
            if ((expires - _clock.Now.ToUniversalTime()).TotalMinutes < refreshMinutes)
            {
                try
                {
                    // 获取用户ID
                    var userIdClaim = jwtSecurityToken.Claims.FirstOrDefault(c => c.Type == "userid")?.Value;
                    if (!string.IsNullOrEmpty(userIdClaim) && long.TryParse(userIdClaim, out var userId))
                    {
                        // 获取设备信息
                        var deviceInfo = jwtSecurityToken.Claims.FirstOrDefault(c => c.Type == "device_info")?.Value ?? "";
                        var ipAddress = httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                        var userAgent = httpContext.Request.Headers["User-Agent"].ToString();

                        // 将旧token标记为无效
                        await _adminToken.RevokeTokenAsync(accessToken);

                        // 创建新token并保存到数据库
                        var tokenEntity = await _dbTokenService.CreateTokenAsync(
                            userId: userId,
                            deviceInfo: deviceInfo,
                            ipAddress: ipAddress,
                            userAgent: userAgent
                        );

                        // 返回新token
                        httpContext.Response.Headers["accesstoken"] = tokenEntity.TokenValue;

                        // 明确告诉浏览器暴露accesstoken响应头
                        httpContext.Response.Headers["Access-Control-Expose-Headers"] = "accesstoken";



                        // 记录日志
                        //_logger.LogInformation($"用户 {userId} 的token已通过无感刷新更新");
                    }
                    else
                    {
                        _logger.LogWarning("无感刷新失败：无法获取用户ID");
                    }
                }
                catch (Exception ex)
                {
                }
            }
            // 即使不需要刷新Token，也更新最后活动时间
            else if (jwtOptions.EnableAutoLogout)
            {
                // 在响应头中添加标记，前端可以据此更新最后活动时间
                httpContext.Response.Headers["X-Last-Activity"] = _clock.Now.ToString("o");
            }
        }
        await Task.CompletedTask;
    }
}