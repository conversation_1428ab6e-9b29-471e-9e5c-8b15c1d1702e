// Copyright © 2024-present wzw

using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.SqlSugar;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts;
using Mapster;
using Volo.Abp;
using OfficeOpenXml;
using YwAdmin.Multiplex.Contracts.helper;
using YwAdmin.Warehouse.InvInStorage.Input;
using OfficeOpenXml.Style;
using YwAdmin.SqlSugar.Entity.WareHouse;
using Volo.Abp.Domain.Repositories;
using YwAdmin.Warehouse.Utility.Const;
using YwAdmin.Warehouse.Utility.Helper;
using YwAdmin.Core.Extensions;
using YwAdmin.Warehouse.InvInStorage.Dtos;

namespace YwAdmin.Warehouse.InvInStorage;

/// <summary>
/// 入库服务
/// </summary>
/// <param name="db">数据</param>
/// <param name="currentUser"></param>
/// <param name="userRepository">用户表</param>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.WAREHOUSE)]
public class InvInStorageService(
    ISqlSugarClient db,
    ICurrentUser currentUser,
    Repository<UserEntity> userRepository,
    Repository<PurchaseOrderEntity> purchaseOrderRepository,
    Repository<PurchaseOrderDetail> purchaseOrderDetailRepository,
    Repository<Inbound> inboundRepository,
    Repository<InboundDetail> inboundDetailRepository
    ) : WarehouseServiceBase
{
    private readonly Repository<PurchaseOrderDetail> _purchaseOrderDetailRepository = purchaseOrderDetailRepository;
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<UserEntity> _userRepository = userRepository;
    private readonly ICurrentUser _currentUser = currentUser;
    private readonly Repository<PurchaseOrderEntity> _purchaseOrderRepository = purchaseOrderRepository;
    private readonly Repository<Inbound> _inboundRepository = inboundRepository;
    private readonly Repository<InboundDetail> _inboundDetailRepository = inboundDetailRepository;


    /// <summary>
    /// 采购入库-确认入库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<TableData> ConfirmPurchaseInbound(InboundDetailInput input)
    {
        try
        {
            // 获取到采购入库主表数据
            var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(input.Id);
            if (purchaseOrder.OrderStatus == WareHouseConst.OrderStatus.STORE)
            {
                throw new UserFriendlyException(L["CurrenStoreNotMsg"]);
            }
            if (purchaseOrder.OrderStatus != WareHouseConst.OrderStatus.CONFIRM)
            {
                throw new UserFriendlyException(L["DataException"]);
            }
            //更新确认人和确认时间
            purchaseOrder.ReceiptUserId = _currentUser.GetUserId();
            purchaseOrder.ReceiptDate = DateTime.Now;
            purchaseOrder.OrderStatus = WareHouseConst.InvType.RAW;
            await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
            string receiptNo = "";
            // 生成入库流水单号
            var strDate = "IN" + DateTime.Now.ToString("yyyyMM");
            var maxId = await _db.Queryable<Inbound>().Where(x => x.ReceiptNo.Contains(strDate)).OrderByDescending(x => x.ReceiptNo).Select(x => x.ReceiptNo).FirstAsync();
            if (string.IsNullOrEmpty(maxId))
            {
                receiptNo = strDate + "00001";
            }
            else
            {
                string codeEnd = maxId.Substring(maxId.Length - 4);
                if (int.TryParse(codeEnd, out int lastTwoDigits))
                {
                    int newFGCode = lastTwoDigits + 1; // 加 1
                    receiptNo = strDate + newFGCode.ToString().PadLeft(5, '0');
                }
            }
            var inbound = new Inbound();
            inbound.ReceiptNo = receiptNo;
            inbound.ReceiptDate = DateTime.Now;
            inbound.SupplierNo = purchaseOrder.Supplier;
            inbound.InvType = WareHouseConst.InvType.RAW;// 原材料
            inbound.OrderNo = purchaseOrder.OrderNo;
            inbound.WarehouseCode = input.WarehouseCode;
            await _inboundRepository.InsertReturnIdAsync(inbound);

            // 获取到采购入库明细数据
            var purchaseOrderDetailList = await _purchaseOrderDetailRepository.GetListAsync(x => x.OrderNo == purchaseOrder.OrderNo);
            //循环purchaseOrderDetailList新增到inboundDetail
            foreach (var purchaseOrderDetail in purchaseOrderDetailList)
            {
                var inboundDetail = new InboundDetail
                {
                    ReceiptNo = inbound.ReceiptNo,
                    Seq = purchaseOrderDetail.Seq,
                    MCode = purchaseOrderDetail.MCode,
                    ItemCode = purchaseOrderDetail.ItemCode,
                    ItemName = purchaseOrderDetail.ItemName,
                    SpecModel = purchaseOrderDetail.SpecModel,
                    Unit = purchaseOrderDetail.Unit,
                    Qty = purchaseOrderDetail.Qty,
                    PackageCount = purchaseOrderDetail.PackageCount,
                    GrossWeight = purchaseOrderDetail.GrossWeight,
                    NetWeight = purchaseOrderDetail.NetWeight,
                    Volume = purchaseOrderDetail.Volume,
                    UnitPrice = purchaseOrderDetail.UnitPrice,
                    Amount = purchaseOrderDetail.Amount,
                    Currency = purchaseOrderDetail.Currency,
                    InvType = WareHouseConst.InvType.RAW,
                    WarehouseCode = input.WarehouseCode,
                    SupplierNo = purchaseOrder.Supplier,
                    ReceiptDate = DateTime.Now,
                };
                await _inboundDetailRepository.InsertReturnIdAsync(inboundDetail);
            }
            return new TableData
            {
                Code = 0,
                Message = "保存成功",
                Data = purchaseOrder
            };
        }
        catch (Exception exc)
        {
            throw new UserFriendlyException(exc.Message);
        }


    }

    [HttpPost]
    public async Task<TableData> GetInboundList(InboundInput input)
    {
        var query = _db.Queryable<Inbound>()
            .LeftJoin<UserEntity>((inb, user) => inb.CreateUserId == user.Id);

        if (!input.ReceiptNo.IsEmpty())
        {
            query = query.Where(x => x.ReceiptNo == input.ReceiptNo);
        }

        var totalCount = await query.CountAsync();
        query = query.InputPage(input).ApplyFilters(input.Filters).ApplySort(input.SortModel);

        var resultList = await query.Select((inb, user) => new InboundOutput
        {
            Id = inb.Id,
            Receiptno = inb.ReceiptNo,
            Receiptdate = inb.ReceiptDate,
            Supplierno = inb.SupplierNo,
            Invtype = inb.InvType,
            Orderno = inb.OrderNo,
            Warehousecode = inb.WarehouseCode,
            CreateUserId = inb.CreateUserId,
            CreateTime = inb.CreateTime,
            UpdateUserId = inb.UpdateUserId,
            UpdateTime = inb.UpdateTime,
            CreateUserName = user.Name,
            UpdateUserName = user.Name
        }).ToListAsync();

        return new TableData
        {
            Code = 0,
            Message = "查询成功",
            Data = resultList,
            Total = totalCount
        };
    }
}