﻿

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using YwAdmin.Core.Cache;
using YwAdmin.Core.Ip2region;
using YwAdmin.Core.Signalr;
using YwAdmin.Core.SnowFlakeId;
using YwAdmin.SqlSugar;

using Serilog;
using Serilog.Events;

using Volo.Abp.AspNetCore.SignalR;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.FileSystem;
using Volo.Abp.Modularity;
using Volo.Abp.Timing;

namespace YwAdmin.Core
{
    [DependsOn(typeof(AdminSqlSugarModule), typeof(AbpAspNetCoreSignalRModule), typeof(AbpBlobStoringFileSystemModule))]
    public class AdminCoreModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();

            context.Services.AddSnowflakeId();
            context.Services.AddPurestCache();
            context.Services.AddPurestSignalr();
            context.Services.AddIp2region();

            ConfigSerilog(context);
            ConfigClock();
        }

        private void ConfigClock()
        {
            Configure<AbpClockOptions>(options =>
            {
                options.Kind = DateTimeKind.Local;
            });
        }


        private void ConfigSerilog(ServiceConfigurationContext context)
        {
            var template = "[{Timestamp:HH:mm:ss}] [{Level:u3}] {SourceContext} {NewLine}{Message:lj}{NewLine}{Exception}{NewLine}";
            context.Services.AddSerilog(options =>
            {
                options.MinimumLevel.Override("Microsoft", LogEventLevel.Warning);
                options.WriteTo.Console();
                options.WriteTo.File($"{AppContext.BaseDirectory}/logs/.txt", LogEventLevel.Warning, template, rollingInterval: RollingInterval.Day, shared: true);
            });
        }
    }
}
