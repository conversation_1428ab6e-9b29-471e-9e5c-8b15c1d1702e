

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;
using System.Security.Cryptography;
using YwAdmin.Application.UserServices.Dtos;
using YwAdmin.Core.DataEncryption.Encryptions;
using YwAdmin.Multiplex.AdminUser;
using YwAdmin.Multiplex.Contracts.IAdminUser;

namespace YwAdmin.Application.CurrentUserContext
{
    /// <summary>
    /// 获取上下文信息
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
    public class CurrentUserContext(ISqlSugarClient db,
        IHttpContextAccessor httpContextAccessor
        ) : ApplicationService,ICurrentUserContext
    {
        private readonly ISqlSugarClient _db = db;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

        /// <summary>
        /// 获取UserId
        /// </summary>
        /// <returns></returns>
        public long GetUserId()
        {
            // 获取 "userid" Claim 的值并转换为 long 类型
            var userIdString = _httpContextAccessor?.HttpContext?.User?.Claims
                .FirstOrDefault(c => c.Type == "userid")?.Value;

            if (long.TryParse(userIdString, out var userId))
            {
                return userId;
            }
            return 1;
        }

        /// <summary>
        /// 获取用户名
        /// </summary>
        /// <returns></returns>
        public string GetUsername()
        {
            return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(c => c.Type == "name").Value;
        }
        public string GetEmail()
        {
            return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(c => c.Type == "email").Value;
        }
        public string GetAccount()
        {
            return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(c => c.Type == "account").Value;
        }

        public string GetRoles()
        {
            //return _httpContextAccessor?.HttpContext?.User?.Claims
            //    .Where(c => c.Type == "role" || c.Type == ClaimTypes.Role)
            //    .Select(c => c.Value) ?? Enumerable.Empty<string>();
            return "";
        }
        public UserContext GetUserInfo()
        {
            var currentUser = new UserContext();
            currentUser.UserId = GetUserId();
            currentUser.Name = GetUsername();
            currentUser.Account = GetAccount();
            currentUser.Email = GetEmail();

            return currentUser;
        }

    }
};

