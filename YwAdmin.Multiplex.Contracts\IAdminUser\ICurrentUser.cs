﻿

namespace YwAdmin.Multiplex.Contracts.IAdminUser;
/// <summary>
/// 当前用户
/// </summary>
public interface ICurrentUser
{
    /// <summary>
    /// 用户 Id
    /// </summary>
    long Id { get; }

    /// <summary>
    /// 组织机构Id
    /// </summary>
    long OrganizationId { get; }
    /// <summary>
    /// 获取用户ID
    /// </summary>
    /// <returns></returns>
     long GetUserId();
    /// <summary>
    /// 获取用户名
    /// </summary>
    /// <returns></returns>
    string GetUsername();

    /// <summary>
    /// 获取邮箱
    /// </summary>
    /// <returns></returns>
    string GetEmail();
    /// <summary>
    /// 获取账号
    /// </summary>
    /// <returns></returns>
    string GetAccount();
    /// <summary>
    /// 获取角色
    /// </summary>
    /// <returns></returns>
    string GetRoles();
    /// <summary>
    /// 获取用户信息
    /// </summary>
    /// <returns></returns>
    UserContext GetUserInfo();

}
