﻿

using System.Globalization;

using SqlSugar;

using Volo.Abp.DependencyInjection;

namespace YwAdmin.Zero;
public class AutoService(ISqlSugarClient db) : ISingletonDependency
{
    private readonly ISqlSugarClient _db = db;
    private static readonly string[] sourceArray = ["string", "byte[]"];
    public void Initialization()
    {
        Console.WriteLine("请选择数据表");
        var tables = _db.DbMaintenance.GetTableInfoList(false);
        for (int i = 0; i < tables.Count; i++)
        {
            Console.WriteLine($"{i}\t{tables[i].Name}");
        }
        var replay = Console.ReadLine() ?? "0";

        var table = tables[int.Parse(replay)];
        Console.WriteLine($"您选择的表是：{table.Name}，请输入类名,如果直接回车则使用默认类名");
        var className = Console.ReadLine();
        if (className.IsNullOrEmpty())
        {
            var nameList = table.Name.Split('_').ToList();
            if (nameList.Count > 1)
            {
                nameList.RemoveAt(0);
            }
            TextInfo ti = new CultureInfo("en-US", false).TextInfo;
            className = nameList.Aggregate("", (current, fName) => current + ti.ToTitleCase(fName.ToLower()));
        }
        Console.WriteLine($"您的类名为：{className}");
        Console.WriteLine($"请输入类库名,如果直接回车则使用默认类类库");
        var applicationName = Console.ReadLine();
        if (applicationName.IsNullOrEmpty())
        {
            applicationName = "Application";
        }
        Console.WriteLine($"您的类库为：{applicationName}");
        CreateService(table,applicationName, className);
    }

    public void CreateService(DbTableInfo table,string Application, string className)
    {
        Console.WriteLine($"请输入命名空间，如直接回车则为默认命名空间YwAdmin.{Application}.{className}Services");
        var nameSpace = Console.ReadLine();
        if (nameSpace.IsNullOrEmpty())
        {
            nameSpace = $"YwAdmin.{Application}.{className}Services";
        }
        Console.WriteLine($"您的命名空间为：{nameSpace}");

        var columns = _db.DbMaintenance.GetColumnInfosByTableName(table.Name, false);
        var outputProps = Common.GetOutputDtoString(columns);
        var inputProps = Common.GetInputDtoString(columns);


        var basePath = AppDomain.CurrentDomain.BaseDirectory;
        var servicePath = Path.Combine(basePath, "Services", $"{className}Services");
        var dtoPath = Path.Combine(servicePath, "Dtos");
        var inputPath = Path.Combine(servicePath, "Input");
        Directory.CreateDirectory(dtoPath);
        Directory.CreateDirectory(inputPath);

        //生成Input
        using (var reader = new StreamReader(Path.Combine(basePath, "Templates", "InputTemplate.txt")))
        {
            var content = reader.ReadToEnd();
            var result = content.Replace("@NameSpace@", nameSpace).Replace("@ClassName@", className).Replace("@props@", inputProps);
            using StreamWriter writer = new(Path.Combine(inputPath, $"{className}Input.cs"));
            writer.Write(result);
        };
        Console.WriteLine($"生成{className}Input.cs");
        //生成AddInput
        using (var reader = new StreamReader(Path.Combine(basePath, "Templates", "AddTemplate.txt")))
        {
            var content = reader.ReadToEnd();
            var result = content.Replace("@NameSpace@", nameSpace).Replace("@ClassName@", className).Replace("@props@", inputProps);
            using StreamWriter writer = new(Path.Combine(dtoPath, $"Add{className}Dto.cs"));
            writer.Write(result);
        };
        Console.WriteLine($"生成Add{className}Dto.cs");
        //生成Output
        using (var reader = new StreamReader(Path.Combine(basePath, "Templates", "OutputTemplate.txt")))
        {
            var content = reader.ReadToEnd();
            var result = content.Replace("@NameSpace@", nameSpace).Replace("@ClassName@", className).Replace("@props@", outputProps);
            using StreamWriter writer = new(Path.Combine(dtoPath, $"{className}Output.cs"));
            writer.Write(result);
        };
        Console.WriteLine($"生成{className}Output.cs");
        //生成DTO
        using (var reader = new StreamReader(Path.Combine(basePath, "Templates", "DtoTemplate.txt")))
        {
            var content = reader.ReadToEnd();
            var result = content.Replace("@NameSpace@", nameSpace).Replace("@ClassName@", className).Replace("@props@", outputProps);
            using StreamWriter writer = new(Path.Combine(dtoPath, $"{className}Dto.cs"));
            writer.Write(result);
        };
        Console.WriteLine($"生成{className}Dto.cs");


        //生成service
        using (var reader = new StreamReader(Path.Combine(basePath, "Templates", "ServiceTemplate.txt")))
        {
            var content = reader.ReadToEnd();
            var result = content.Replace("@NameSpace@", nameSpace).Replace("@ClassName@", className);
            using StreamWriter writer = new(Path.Combine(servicePath, $"{className}Service.cs"));
            writer.Write(result);
        };
        Console.WriteLine($"生成{className}Service.cs");
        Console.WriteLine($"Services生成成功，路径为{servicePath}");
    }
}
