﻿using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.SqlSugar;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.Production.SubModules.ProductServices.Dtos;
using Mapster;
using YwAdmin.Production.BomServices.Dtos;
using Volo.Abp;
using YwAdmin.Production.SubModules.BomServices.Dtos;

namespace YwAdmin.Production.SubModules.ProductServices;

/// <summary>
/// 商品编码
/// </summary>
/// <param name="db"></param>
/// <param name="currentUser"></param>
/// <param name="userRepository"></param>
/// <param name="productRepository"></param>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.PRODUCTION)]
public class ProductService(
    ISqlSugarClient db,
    ICurrentUser currentUser,
    Repository<UserEntity> userRepository,
      Repository<ProductCodes> productRepository
    ) : ProductionServiceBase
{
    private readonly Repository<ProductCodes> _productRepository = productRepository;
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<UserEntity> _userRepository = userRepository;

    /// <summary>
    /// 查询商品编码数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetProduct(ProductInput input)
    {
        var query = _db.Queryable<ProductCodes>();
        if (!string.IsNullOrEmpty(input.HSCode))
        {
            query = query.Where(x => x.HSCode.Contains(input.HSCode));
        }
        if (!string.IsNullOrEmpty(input.ProductName))
        {
            query = query.Where(x => x.ProductName.Contains(input.ProductName));
        }
        if (!string.IsNullOrEmpty(input.Category))
        {
            query = query.Where(x => x.Category.Contains(input.Category));
        }
        var results = await query.ToListAsync();
        var resultList = results.Select(
              result =>
              {
                  var dto = result.Adapt<ProductDto>();
                  dto.CreateUserName = _userRepository.GetById(result.CreateUserId)?.Name;
                  dto.UpdateUserName = _userRepository.GetById(result.UpdateUserId)?.Name;
                  return dto;
              });
        return new TableData { Data = resultList };
    }

    /// <summary>
    /// 新增或者更新商品编码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    ///
    [HttpPost]
    public async Task<TableData> AddOrEditProduct(AddProductDto input)
    {
        if (input.Id == null)
        {
            if (string.IsNullOrEmpty(input.HSCode))
            {
                throw new UserFriendlyException(L["InputProduCode"]);
            }
            if (string.IsNullOrEmpty(input.ProductName))
            {
                throw new UserFriendlyException(L["InputProduName"]);
            }
            //检查商品编号是否重复
            var productInfo = await _productRepository.GetFirstAsync(x => x.HSCode == input.HSCode);
            if (productInfo != null)
            {
                throw new UserFriendlyException(L["HsCodeIsHas"]);
            }
            var entity = input.Adapt<ProductCodes>();
            await _productRepository.InsertAsync(entity);
        }
        else
        {
            var productInfo = await _db.Queryable<ProductCodes>().FirstAsync(x => x.Id == input.Id);
            if (productInfo != null)
            {
                productInfo.MeterUnit = input.MeterUnit;
                productInfo.ProductDec = input.ProductDec;
                productInfo.ProductName = input.ProductName;
                productInfo.Remark = input.Remark;
                productInfo.SubCategory = input.SubCategory;
                productInfo.TradUnit = input.TradUnit;
                productInfo.Category = input.Category;
                await _productRepository.UpdateAsync(productInfo);
            }
            else
            {
                throw new UserFriendlyException(L["DataException"]);
            }
        }
        return new TableData { Data = L["SaveSuccessfule"] };
    }

    /// <summary>
    /// 删除商品编码数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> DeleteProduct(ProductInput input)
    {
        var product = await _productRepository.GetFirstAsync(x => x.Id == input.Id);
        if (product == null)
        {
            throw new UserFriendlyException(L["Abnormal"]);
        }
        await _productRepository.DeleteAsync(product);
        return new TableData { Data = L["DeleteSuccessful"] };
    }

    /// <summary>
    /// 查询商品编码明细数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> GetProductInfo(ProductInput input)
    {
        if (input.Id == null | input.Id == 0)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        var entity = await _db.Queryable<ProductCodes>().FirstAsync(x => x.Id == input.Id);
        var result = entity.Adapt<ProductDto>();
        result.CreateUserName = _userRepository.GetById(entity.CreateUserId)?.Name;
        result.UpdateUserName = _userRepository.GetById(entity.UpdateUserId)?.Name;
        return new TableData
        {
            Data = result,
        };
    }
}