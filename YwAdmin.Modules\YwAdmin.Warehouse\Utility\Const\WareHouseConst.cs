﻿// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YwAdmin.Warehouse.Utility.Const;
public class WareHouseConst
{
    /// <summary>
    /// 入库类型
    /// </summary>
    public static class InvType
    {
        /// <summary>
        /// 原材料
        /// </summary>
        public const string RAW = "RAW";
        /// <summary>
        /// 半成品
        /// </summary>
        public const string WIP = "WIP";
        /// <summary>
        /// 禁止物料
        /// </summary>
        public const string BAN = "BAN";
        /// <summary>
        /// 残次品
        /// </summary>
        public const string DEF = "DEF";
    }

    /// <summary>
    /// 订单状态
    /// </summary>
    public static class OrderStatus
    {
        /// <summary>
        /// 入库
        /// </summary>
        public const string STORE = "store";
        /// <summary>
        /// 新建
        /// </summary>
        public const string NEW = "new";
        /// <summary>
        /// 确认
        /// </summary>
        public const string CONFIRM = "confirm";
    }

}
