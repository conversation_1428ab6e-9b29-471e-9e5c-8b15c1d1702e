﻿// Copyright © 2024-present wzw

using System.Globalization;

namespace YwAdmin.Api.Host.Options;

public class LanguageMiddleware
{
    private readonly RequestDelegate _next;

    public LanguageMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var culture = context.Request.Headers["Accept-Language"].ToString();

        // 这里可以清理掉 "q" 值，比如从 "zh-CN,zh;q=0.9" 中提取 "zh-CN"
        var validCulture = GetValidCulture(culture);

        // 确保有效文化存在
        if (!string.IsNullOrEmpty(validCulture))
        {
            CultureInfo.CurrentCulture = new CultureInfo(validCulture);
            CultureInfo.CurrentUICulture = new CultureInfo(validCulture);
        }

        await _next(context);
    }

    private string GetValidCulture(string culture)
    {
        // 从 "zh-CN,zh;q=0.9" 中提取 "zh-CN" 或 "zh"
        var cultures = culture.Split(',')
            .Select(c => c.Split(';')[0])  // 去掉优先级部分 (q=0.9)
            .FirstOrDefault(c => IsValidCulture(c));  // 获取第一个有效的文化

        return cultures;
    }

    private bool IsValidCulture(string culture)
    {
        // 这里检查是否是有效的文化标识符
        try
        {
            var ci = new CultureInfo(culture);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

