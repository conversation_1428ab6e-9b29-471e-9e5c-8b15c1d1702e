﻿using YwAdmin.Multiplex.Contracts.IAdminUser;
namespace YwAdmin.SqlSugar;
public class Repository<T> : SimpleClient<T> where T : BaseEntity, new()
{
    private readonly ICurrentUser _currentUser;
    public Repository(ISqlSugarClient db, ICurrentUser currentUser)
    {
        base.Context = db;
        _currentUser = currentUser;
    }

    // 异步延迟加载 GetAll() 方法
    //public ISugarQueryable<T> GetAll()
    //{
    //    //return this.Context.Queryable<T>().AsQueryable();
    //    return this.Context.Queryable<T>();
    //}

    //新增数据创建ID
    public virtual async Task<long> InsertReturnIdAsync(T insertObj)
    {
        if (insertObj.CreateUserId == default)
        {
            insertObj.CreateUserId = _currentUser.Id;
            insertObj.CreateTime = DateTime.Now;
        }
        insertObj.UpdateUserId = _currentUser.Id;
        insertObj.UpdateTime = DateTime.Now;
        return await Context.Insertable(insertObj).ExecuteReturnSnowflakeIdAsync();
    }

    public virtual async Task<bool> InsertAsync(T insertObj)
    {
        if (insertObj.CreateTime == default)
        {
            insertObj.CreateTime = DateTime.Now;
        }
        if (insertObj.CreateUserId == default)
        {
            insertObj.CreateUserId = _currentUser.Id;
        }
        return await Context.Insertable(insertObj).IgnoreColumns(it => it.Id).ExecuteCommandAsync() > 0;
    }

    //更新数据
    public virtual async Task<bool> UpdateAsync(T updateObj)
    {
        if (updateObj.UpdateTime == default)
        {
            updateObj.UpdateTime = DateTime.Now;
        }
        if (updateObj.UpdateUserId == default)
        {
            updateObj.UpdateUserId = _currentUser.Id;
        }
        return await Context.Updateable(updateObj).ExecuteCommandAsync() > 0;
    }
}
