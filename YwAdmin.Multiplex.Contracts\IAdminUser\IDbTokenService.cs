using YwAdmin.Multiplex.Contracts.Models;

namespace YwAdmin.Multiplex.Contracts.IAdminUser;

/// <summary>
/// 数据库Token管理服务接口
/// </summary>
public interface IDbTokenService
{
    /// <summary>
    /// 创建新Token
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deviceInfo">设备信息</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="userAgent">用户代理</param>
    /// <returns>创建的Token实体</returns>
    Task<UserTokenDto> CreateTokenAsync(long userId, string deviceInfo, string ipAddress, string userAgent);

    /// <summary>
    /// 刷新Token
    /// </summary>
    /// <param name="refreshToken">刷新Token</param>
    /// <returns>新的Token实体</returns>
    Task<UserTokenDto> RefreshTokenAsync(string refreshToken);

    /// <summary>
    /// 验证Token
    /// </summary>
    /// <param name="token">Token值</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateTokenAsync(string token);

    /// <summary>
    /// 撤销Token
    /// </summary>
    /// <param name="token">Token值</param>
    /// <returns>操作结果</returns>
    Task<bool> RevokeTokenAsync(string token);

    /// <summary>
    /// 撤销用户所有Token
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    Task<bool> RevokeAllUserTokensAsync(long userId);


    /// <summary>
    /// 更新Token最后活动时间
    /// </summary>
    /// <param name="token">Token值</param>
    /// <returns>操作结果</returns>
    Task<bool> UpdateLastActivityAsync(string token);

    /// <summary>
    /// 获取用户所有有效Token
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>Token列表</returns>
    Task<List<UserTokenDto>> GetUserActiveTokensAsync(long userId);

    /// <summary>
    /// 清理过期Token
    /// </summary>
    /// <returns>清理数量</returns>
    Task<int> CleanupExpiredTokensAsync();
}
