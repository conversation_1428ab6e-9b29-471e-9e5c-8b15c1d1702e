
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[SYS_USER]') AND type IN ('U'))
	DROP TABLE [dbo].[SYS_USER]
GO
--用户表
CREATE TABLE [dbo].[SYS_USER] (
  [ID] bigint  NOT NULL,
  [CREATEUSERID] bigint  NOT NULL,
  [CREATETIME] datetime  NOT NULL,
  [UPDATATEUSERID] bigint  NULL,
  [UPDATETIME] datetime  NULL,
  [REMARK] varchar(1000) COLLATE Chinese_PRC_CI_AS  NULL,
  [ACCOUNT] varchar(36) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [PASSWORD] varchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [NAME] varchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [TELEPHONE] varchar(11) COLLATE Chinese_PRC_CI_AS  NULL,
  [EMAIL] varchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [AVATAR] binary(1)  NULL,
  [STATUS] numeric(18)  NULL,
  [ORGANIZATION_ID] bigint  NOT NULL
)
GO

ALTER TABLE [dbo].[SYS_USER] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'ID'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'CREATEUSERID'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'CREATETIME'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改人',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'UPDATATEUSERID'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改时间',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'UPDATETIME'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'REMARK'
GO

EXEC sp_addextendedproperty
'MS_Description', N'账号',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'ACCOUNT'
GO

EXEC sp_addextendedproperty
'MS_Description', N'密码',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'PASSWORD'
GO

EXEC sp_addextendedproperty
'MS_Description', N'真实姓名',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'NAME'
GO

EXEC sp_addextendedproperty
'MS_Description', N'电话',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'TELEPHONE'
GO

EXEC sp_addextendedproperty
'MS_Description', N'邮箱',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'EMAIL'
GO

EXEC sp_addextendedproperty
'MS_Description', N'头像',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'AVATAR'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'STATUS'
GO

EXEC sp_addextendedproperty
'MS_Description', N'组织机构Id',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER',
'COLUMN', N'ORGANIZATION_ID'
GO

EXEC sp_addextendedproperty
'MS_Description', N'用户',
'SCHEMA', N'dbo',
'TABLE', N'SYS_USER'
GO


-- ----------------------------
-- Records of SYS_USER
-- ----------------------------
INSERT INTO [dbo].[SYS_USER] ([ID], [CREATEUSERID], [CREATETIME], [UPDATATEUSERID], [UPDATETIME], [REMARK], [ACCOUNT], [PASSWORD], [NAME], [TELEPHONE], [EMAIL], [AVATAR], [STATUS], [ORGANIZATION_ID]) VALUES (N'1', N'1', N'2025-01-17 19:36:53.000', NULL, N'2025-01-17 19:36:58.000', N'管理员', N'admin', N'e10adc3949ba59abbe56e057f20f883e', N'管理员', NULL, N'<EMAIL>', NULL, NULL, N'1')
GO


-- ----------------------------
-- Uniques structure for table SYS_USER
-- ----------------------------
ALTER TABLE [dbo].[SYS_USER] ADD CONSTRAINT [UK_PUREST_USER_ACCOUNT] UNIQUE NONCLUSTERED ([ACCOUNT] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table SYS_USER
-- ----------------------------
ALTER TABLE [dbo].[SYS_USER] ADD CONSTRAINT [PK_PUREST_USER] PRIMARY KEY NONCLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Foreign Keys structure for table SYS_USER
-- ----------------------------
ALTER TABLE [dbo].[SYS_USER] ADD CONSTRAINT [FK_PUREST_U_REFERENCE_PUREST_O] FOREIGN KEY ([ORGANIZATION_ID]) REFERENCES [dbo].[PUREST_ORGANIZATION] ([ID]) ON DELETE CASCADE ON UPDATE NO ACTION
GO

