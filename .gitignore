﻿################################################################################
# 此 .gitignore 文件已由 Microsoft(R) Visual Studio 自动创建，并已更新。
################################################################################

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates
*.csproj.user

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017 cache/options directory
.vs/

# NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!?*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
/YwAdmin.Production/obj/project.assets.json
/YwAdmin.Api.Host/YwAdmin.Api.Host.csproj.user
/YwAdmin.Production/obj/project.nuget.cache
/YwAdmin.Production/obj/YwAdmin.Production.csproj.nuget.dgspec.json
/YwAdmin.Production/obj/YwAdmin.Production.csproj.nuget.g.props
/.vscode
