

namespace YwAdmin.Application.UserServices.Dtos;

/// <summary>
/// 用户查询
/// </summary>
public class GetPagedListInput : PaginationParams
{
    /// <summary>
    /// 账号
    /// </summary>
    public string Account { get; set; }
    /// <summary>
    /// 用户名
    /// </summary>
    public string Name { get; set; }
    /// <summary>
    /// 电话
    /// </summary>
    public string Telephone { get; set; }
    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; }
    /// <summary>
    /// 账户状态
    /// </summary>
    public int? Status { get; set; }


    protected override Dictionary<string, string> PrefixMap { get; } = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
    {

    };
}