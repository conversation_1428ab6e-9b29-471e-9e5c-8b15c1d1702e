// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.Purchase.PurchaseOrderDetailServices.Dtos;

/// <summary>
/// 新增PurchaseOrderDetail表DTO
/// </summary>
public class AddPurchaseOrderDetailDto : BaseDto
{
    /// <summary>
    /// 订单号
    /// </summary>
    [Required(ErrorMessage = "订单号不能为空"), MaxLength(50, ErrorMessage = "订单号最大长度为：50")]
    public string Orderno { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    [Required(ErrorMessage = "序号不能为空")]
    public int Seq { get; set; }

    /// <summary>
    /// 原材料料号
    /// </summary>
    public string Mcode { get; set; }

    /// <summary>
    /// 商品编号
    /// </summary>
    [Required(ErrorMessage = "商品编号不能为空"), MaxLength(50, ErrorMessage = "商品编号最大长度为：50")]
    public string Itemcode { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string Itemname { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string Specmodel { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [Required(ErrorMessage = "数量不能为空")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 件数
    /// </summary>
    public int? Packagecount { get; set; }

    /// <summary>
    /// 毛重
    /// </summary>
    public decimal? Grossweight { get; set; }

    /// <summary>
    /// 净重
    /// </summary>
    public decimal? Netweight { get; set; }

    /// <summary>
    /// 体积
    /// </summary>
    public decimal? Volume { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Unitprice { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }
}