﻿// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.SqlSugar;

namespace YwAdmin.SqlSugar.Entity.Basic;

/// <summary>
/// 公司信息表
/// </summary>
[SugarTable("Base_CompanyInfo")]
public partial class CompanyInfo : BaseEntity
{

    /// <summary>
    /// 公司代码（系统内部识别用）
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 公司税号
    /// </summary>
    public string TaxCode { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 邮政编码
    /// </summary>
    public string PostalCode { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    public string Phone { get; set; }

    /// <summary>
    /// 联系人名称
    /// </summary>
    public string ContactName { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>
    public string ContactPhone { get; set; }

    /// <summary>
    /// 邮件
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 国家代码（如 CN、SG、US 等）
    /// </summary>
    public string CountryCode { get; set; }

    /// <summary>
    /// 类型代码（如：1生产企业，2贸易公司，3其他）
    /// </summary>
    public string TypeCode { get; set; }

    /// <summary>
    /// 海关当局
    /// </summary>
    public string CustomsAuthority { get; set; }

    /// <summary>
    /// 货物分类代码
    /// </summary>
    public string GoodsClassificationCode { get; set; }

    /// <summary>
    /// 个人/组织分类（如 person / organization）
    /// </summary>
    public string PersonOrgType { get; set; }

    /// <summary>
    /// 申报单处理部门代码
    /// </summary>
    public string DeclarationDeptCode { get; set; }

    /// <summary>
    /// 运输方式代码
    /// </summary>
    public string TransportModeCode { get; set; }

}



