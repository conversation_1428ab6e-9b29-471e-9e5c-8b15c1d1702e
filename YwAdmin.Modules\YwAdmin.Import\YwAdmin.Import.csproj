﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Localization\Production\en.json" />
    <None Remove="Localization\Production\vi.json" />
    <None Remove="Localization\Production\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\Production\en.json" />
    <EmbeddedResource Include="Localization\Production\vi.json" />
    <EmbeddedResource Include="Localization\Production\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="SubModules\CustomsDeclarationServices\Dtos\" />
    <Folder Include="SubModules\CustomsDeclarationServices\Input\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\YwAdmin.Core\YwAdmin.Core.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex.Contracts\YwAdmin.Multiplex.Contracts.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex\YwAdmin.Multiplex.csproj" />
    <ProjectReference Include="..\..\YwAdmin.SqlSugar\YwAdmin.SqlSugar.csproj" />
  </ItemGroup>

</Project>
