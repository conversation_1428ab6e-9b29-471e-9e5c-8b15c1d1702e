// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.Warehouse.InvInStorage.Dtos;

/// <summary>
/// InboundDetail表DTO
/// </summary>
public class InboundDetailDto : BaseDto
{
	/// <summary>
	/// 
	/// </summary>
	public long Id { get; set; }
	/// <summary>
	/// 入库单号
	/// </summary>
	public string Receiptno { get; set; }
	/// <summary>
	/// 序号
	/// </summary>
	public int Seq { get; set; }
	/// <summary>
	/// 原材料料号
	/// </summary>
	public string Mcode { get; set; }
	/// <summary>
	/// 商品编号
	/// </summary>
	public string Itemcode { get; set; }
	/// <summary>
	/// 商品名称
	/// </summary>
	public string Itemname { get; set; }
	/// <summary>
	/// 规格型号
	/// </summary>
	public string Specmodel { get; set; }
	/// <summary>
	/// 单位
	/// </summary>
	public string Unit { get; set; }
	/// <summary>
	/// 数量
	/// </summary>
	public decimal? Qty { get; set; }
	/// <summary>
	/// 件数
	/// </summary>
	public int? Packagecount { get; set; }
	/// <summary>
	/// 毛重
	/// </summary>
	public decimal? Grossweight { get; set; }
	/// <summary>
	/// 净重
	/// </summary>
	public decimal? Netweight { get; set; }
	/// <summary>
	/// 体积
	/// </summary>
	public decimal? Volume { get; set; }
	/// <summary>
	/// 单价
	/// </summary>
	public decimal? Unitprice { get; set; }
	/// <summary>
	/// 金额
	/// </summary>
	public decimal? Amount { get; set; }
	/// <summary>
	/// 币种
	/// </summary>
	public string Currency { get; set; }
	/// <summary>
	/// 入库时间
	/// </summary>
	public DateTime? Receiptdate { get; set; }
	/// <summary>
	/// 供应商代码
	/// </summary>
	public string Supplierno { get; set; }
	/// <summary>
	/// 物料类型
	/// </summary>
	public string Invtype { get; set; }
	/// <summary>
	/// 仓库代码
	/// </summary>
	public string Warehousecode { get; set; }
}
