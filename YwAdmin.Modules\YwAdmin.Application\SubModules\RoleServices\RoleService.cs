using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Polly;
using System.Security.Cryptography;
using YwAdmin.Application.CurrentUserContext;
using YwAdmin.Core.DataEncryption.Encryptions;
using YwAdmin.Multiplex.AdminUser;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Application.UserServices.Dtos;
using YwAdmin.Application.RoleServices.Input;
using YwAdmin.Application.RoleServices.Dtos;
using YwAdmin.SqlSugar.Entity;
using SqlSugar.Extensions;
using System.DirectoryServices.ActiveDirectory;
using System.Security.Principal;
using YamlDotNet.RepresentationModel;

namespace YwAdmin.Application.RoleServices
{
    /// <summary>
    /// 角色服務
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
    public class RoleService(
        ISqlSugarClient db,
        Repository<UserEntity> userRepository,
        Repository<Role> roleRepository,
        ICurrentUserContext context,
        Repository<RoleFunction> roleFunctionRepository,
        Repository<UserRoleEntity> userRoleRepository
        ) : ApplicationServiceBase
    {
        private readonly ISqlSugarClient _db = db;
        private readonly Repository<Role> _roleRepository = roleRepository;
        private readonly Repository<RoleFunction> _roleFunctionRepository = roleFunctionRepository;
        private readonly ICurrentUserContext _context = context;
        private readonly Repository<UserRoleEntity> _userRoleRepository = userRoleRepository;
        private readonly Repository<UserEntity> _userRepository = userRepository;

        /// <summary>
        /// 新增用户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        ///
        [AllowAnonymous]
        public async Task<TableData> InsertRole(RoleInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.RoleCode))
                {
                    throw new UserFriendlyException("请输入角色代码！");
                }
                if (string.IsNullOrEmpty(input.RoleName))
                {
                    throw new UserFriendlyException("请输入角色名称！");
                }
                var userInfo = await _db.Queryable<Role>().Where(x => x.RoleCode == input.RoleCode).FirstAsync();
                if (userInfo != null)
                {
                    throw new UserFriendlyException("角色代码重复，请重新输入！");
                }
                var role = input.Adapt<Role>();

                role.CreateUserId = _context.GetUserId();

                await _roleRepository.InsertReturnSnowflakeIdAsync(role);
                return new TableData
                {
                    Message = L["SaveSuccessfule"]
                };
            }
            catch (Exception exc)
            {
                return new TableData { Code = 500, Message = exc.Message };
            }
        }

        /// <summary>
        /// 查询角色
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        ///
        [HttpPost]
        public async Task<TableData> GetRoleList(RoleInput input)
        {
            var query = _db.Queryable<Role>()
           .Select((u) => new Role
           {
               Id = u.Id.SelectAll(),
               //OrganizationName = u.OrganizationId.GetConfigValue<OrganizationEntity>()
           });
            if (!string.IsNullOrEmpty(input.RoleName))
            {
                query = query.Where(x => x.RoleName.Contains(input.RoleName));
            }
            if (!string.IsNullOrEmpty(input.RoleCode))
            {
                query = query.Where(x => x.RoleCode.Contains(input.RoleCode));
            }
            if (!string.IsNullOrEmpty(input.Remark))
            {
                query = query.Where(x => x.Remark.Contains(input.Remark));
            }
            var userList = query.ToList();
            var result = userList.Adapt<List<RoleDto>>();
            //.OrderByDescending(u => u.CreateTime)
            //.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
            //return pagedList.Adapt<PagedList<UserOutput>>();
            return new TableData
            {
                Data = result,
                Message = "查询成功！",
            };
        }

        /// <summary>
        /// 对角色进行授权
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<TableData> GrantPermissions(RolePermInput input)
        {
            try
            {
                //删除当前角色的所有授权
                await _roleFunctionRepository.DeleteAsync(x => x.RoleId == input.RoleId);
                //添加当前选择的功能进行授权
                //var permList = input.Items.Split(',').ToList();
                foreach (var item in input.Perms)
                {
                    var rolePerm = input.Adapt<RoleFunction>();
                    rolePerm.PermCode = item;
                    rolePerm.CreateUserId = _context.GetUserId();
                    await _roleFunctionRepository.InsertReturnSnowflakeIdAsync(rolePerm);
                }

                return new TableData
                {
                    Message = "授权成功！"
                };
            }
            catch (Exception)
            {
                return new TableData
                {
                    Code = 202,
                    Message = "系统异常，请重试，或联系系统管理人员！"
                };
            }
        }

        /// <summary>
        /// 根据角色ID获取已授权功能
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        ///
        [HttpPost]
        public async Task<TableData> GetPermissions(RolePermInput input)
        {
            try
            {
                //删除当前角色的所有授权
                var roleList = await _roleFunctionRepository.GetListAsync(x => x.RoleId == input.RoleId);
                var result = roleList.Select(x => x.PermCode).ToList();
                return new TableData
                {
                    Data = result,
                    Message = "授权成功！"
                };
            }
            catch (Exception)
            {
                return new TableData
                {
                    Code = 202,
                    Message = "系统异常，请重试，或联系系统管理人员！"
                };
            }
        }

        /// <summary>
        /// 根据角色获取用户信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<TableData> GetRoleUser(RoleInput input)
        {
            var query = _db.Queryable<UserRoleEntity, UserEntity>((u, c) => new JoinQueryInfos(
                JoinType.Left, u.UserId == c.Id
            )).Where(u => u.RoleId == input.RoleId)
            .Select((u, c) => new UserOutput
            {
                Id = u.Id,
                Account = c.Account,
                Name = c.Name,
                Telephone = c.Telephone,
                Status = c.Status,
                Email = c.Email,
                Remark = c.Remark
            });
            var resultList = query.ToList();
            return new TableData
            {
                Data = query.ToList(),
            };
        }

        /// <summary>
        /// 为角色添加用户
        /// </summary>
        /// <returns></returns>
        //[HttpPost]
        //public async Task<TableData> InsertRoleUser(RolePermInput input)
        //{
        //    //获取到ID
        //    var ids = input.Items.Split(',').ToList();
        //    foreach (var id in ids)
        //    {
        //        await _db.Insertable(new UserRoleEntity
        //        {
        //            UserId = Convert.ToInt64(id),
        //            RoleId = input.RoleId
        //        }).ExecuteReturnSnowflakeIdAsync();
        //    }
        //    return new TableData
        //    {
        //        Message = "保存成功！",
        //    };

        //}

        /// <summary>
        /// 获取不属于当前角色的用户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        ///
        [HttpPost]
        public async Task<TableData> GetNotRoleUser(RoleInput input)
        {
            // 获取指定角色ID下的所有用户ID
            var roleUserIds = _db.Queryable<UserRoleEntity>()
                .Where(c => c.RoleId == input.RoleId)
                .Select(c => c.UserId)
                .ToList();

            // 查询所有不包含这些用户ID的用户
            var query = _db.Queryable<UserEntity>()
                .Where(u => !roleUserIds.Contains(u.Id))  // 排除指定角色的用户
                .Select(u => new UserOutput
                {
                    Id = u.Id,
                    Account = u.Account,
                    Name = u.Name,
                    Telephone = u.Telephone,
                    Status = u.Status,
                    Email = u.Email,
                    Remark = u.Remark
                });
            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(u => u.Name.Contains(input.Name));
            }
            if (!string.IsNullOrEmpty(input.Telephone))
            {
                query = query.Where(u => u.Telephone.Contains(input.Telephone));
            }
            if (!string.IsNullOrEmpty(input.Email))
            {
                query = query.Where(u => u.Email.Contains(input.Email));
            }
            if (!string.IsNullOrEmpty(input.Account))
            {
                query = query.Where(u => u.Account.Contains(input.Account));
            }
            var resultList = query.ToList();
            return new TableData
            {
                Data = query.ToList(),
            };
        }

        /// <summary>
        /// 为角色添加用户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<TableData> InsertRoleUser(RoleInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Items))
                {
                    return new TableData
                    {
                        Code = 303,
                        Message = "请选择用户保存！",
                    };
                }
                List<long> ids = input.Items
                           .Split(',')  // 按逗号分割字符串
                           .Select(item => Convert.ToInt64(item))  // 将每个项转换为 long
                           .ToList();  // 转换成 List<long>

                foreach (var id in ids)
                {
                    var userRole = new UserRoleEntity();
                    userRole.RoleId = input.RoleId;
                    userRole.UserId = id;
                    await _userRoleRepository.InsertReturnSnowflakeIdAsync(userRole);
                }
                return new TableData { Message = L["SaveSuccessfule"] };
            }
            catch (Exception exc)
            {
                return new TableData { Code = 500, Message = exc.Message };
            }
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<TableData> DeleteRole(RoleInput input)
        {
            //当前角色下有别的角色是不允许删除，或者有用户是当前的角色的也是不可删除
            //检查
            var userRoleInfo = await _userRoleRepository.GetFirstAsync(x => x.RoleId == input.Ids);
            if (userRoleInfo != null)
            {
                throw new UserFriendlyException(L["HasUserCnacelRole"]);
            }
            await _roleRepository.DeleteAsync(x => x.Id == input.Ids);
            //同时删除授权码的数据
            return new TableData { Message = L["DeleteSuccessfule"] };
        }

        /// <summary>
        /// 删除角色关联的用户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<TableData> DeleteUserRole(RoleInput input)
        {
            await _userRoleRepository.DeleteAsync(x => x.Id == input.Id);
            //同时删除授权码的数据
            return new TableData { Message = L["DeleteSuccessfule"] };
        }

        /// <summary>
        /// 给用户添加角色
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<TableData> InsertRoleByUser(UserRoleInput input)
        {
            try
            {
                //删除当前ID的所有角色
                await _userRoleRepository.DeleteAsync(x => x.UserId == input.Id);

                //List<long> ids = input.Items
                //           .Split(',')  // 按逗号分割字符串
                //           .Select(item => Convert.ToInt64(item))  // 将每个项转换为 long
                //           .ToList();  // 转换成 List<long>

                foreach (var id in input.ItemList)
                {
                    var userRole = new UserRoleEntity();
                    userRole.RoleId = id;
                    userRole.UserId = input.Id;
                    await _userRoleRepository.InsertReturnSnowflakeIdAsync(userRole);
                }
                return new TableData { Message = L["SaveSuccessfule"] };
            }
            catch (Exception exc)
            {
                return new TableData { Code = 500, Message = exc.Message };
            }
        }

        /// <summary>
        /// 获取用户对应的角色
        /// </summary>
        /// <param name="inpuit"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<TableData> GetRoleByUser(RoleInput input)
        {
            try
            {
                var query = from s in db.Queryable<UserRoleEntity>()
                            where s.UserId == input.Id
                            select s.RoleId;
                var resultList = query.ToList();
                return new TableData { Data = resultList };
            }
            catch (Exception exc)
            {
                return new TableData { Code = 500, Message = exc.Message };
            }
        }
    }
};