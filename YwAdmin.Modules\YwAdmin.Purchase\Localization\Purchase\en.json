{"culture": "en", "texts": {"Hello": "Hello!", "UpdateSuccessful": "Execution successful!", "Abnormal": "Operation abnormal, please contact the administrator!", "PleaseEnterName": "Please enter a name!", "NotFound": "The data to be deleted was not found, please refresh!", "DeleteChild": "Please delete the corresponding child node first!", "PswNewNotOld": "The new password and confirmation password do not match!", "DataException": "Data exception, please refresh!", "MCodeNull": "Please enter the material code!", "MCodeHas": "The material code you entered already exists!", "OldPswFail": "Old password verification failed!", "StatusErro": "The current status does not allow publishing!", "CancelStatusErro": "The current status does not allow canceling the publishing!", "SaveSuccessfule": "Save successful!", "StatusErro2": "The current status does not allow approval!", "StatusErro3": "The current status does not allow modification!", "DelMCodeStatus": "The material has been published and cannot be modified!", "MCodeUseNotDel": "The material is in use and cannot be deleted!", "MCodeStatus": "The material has been published and cannot be modified!", "DeletedData": "The selected data has been deleted, please refresh and try again", "HasTechNotDel": "The selected finished product still has consumption data, please delete it first!", "StatusNotDel": "The current data status does not allow deletion!", "HsCodeIsHas": "The product code entered is duplicated, please check the data and re-enter!", "DeleteSuccessful": "Delete successful!", "InputProduCode": "Please enter the product code!", "InputProduName": "Please enter the product name!", "OrderNotDel": "The current order status does not allow deletion!", "OrderNoEmpty": "Order number cannot be empty", "CustomsDataNotFound": "Corresponding customs declaration data not found", "CustomsExcelGenerateError": "Failed to generate customs declaration Excel", "ImportCompanyInfo": "【Import Company Information】", "ExportCompanyInfo": "【Export Company Information】", "BillInfo": "【Bill of Lading Information】", "LicenseInfo": "【License Information】", "InvoiceInfo": "【Invoice Information】", "TaxInfo": "【Tax Information】", "CompanyCode": "Company Code", "CompanyTaxCode": "Company Tax Code", "CompanyName": "Company Name", "PostalCode": "Postal Code", "Address": "Address", "PhoneNumber": "Phone Number", "ContactPerson": "Contact Person", "ContactPhone": "Contact Phone Number", "Email": "Email", "TypeCode": "Type Code", "CustomsAuthority": "Customs Authority", "GoodsClassificationCode": "Goods Classification Code", "PersonOrgClassification": "Person/Organization Classification", "DeclarationDeptCode": "Declaration Department Code", "TransportModeCode": "Transport Mode Code", "CountryCode": "Country Code", "BillNo": "Bill of Lading Number", "ATD": "Actual Time of Departure (ATD)", "PackageCount": "Package Count", "GrossWeight": "Gross Weight", "WarehouseLocationCode": "Expected Warehouse Location Code", "TransportMode": "Transport Mode", "ATA": "Actual Time of Arrival (ATA)", "DischargePort": "Discharge Port", "LoadingPort": "Loading Port", "ContainerCount": "Container Count", "LicenseContractNo": "Processing Contract Number", "LicenseContractDate": "Contract Date", "LicenseExpiryDate": "Contract Expiry Date", "LicenseImportNo": "Import License", "InvoiceType": "Invoice Type", "InvoiceNo": "Invoice Number", "InvoiceDate": "Invoice Date", "PaymentMethod": "Payment Method", "InvoiceTypeCode": "Invoice Type Code", "InvoiceAmount": "Invoice Amount", "DeliveryTerms": "Delivery Terms", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "CustomsValueCode": "Customs Value Classification Code", "Freight": "Freight", "Insurance": "Insurance", "TaxDueDateCode": "Tax Due Date Code", "CustomsDeclarationInfo": "Customs Declaration Information", "DetailInfo": "【Detail Information】", "SerialNumber": "Serial No.", "MaterialCode": "Material Code", "ItemCode": "Item Code", "ItemName": "Item Name", "SpecModel": "Specification/Model", "Unit": "Unit", "Quantity": "Quantity", "NetWeight": "Net Weight", "Volume": "Volume", "UnitPrice": "Unit Price", "Amount": "Amount", "CurrenStoreNotMsg": "The current purchase order has already been stocked in, and cannot be operated again!"}}