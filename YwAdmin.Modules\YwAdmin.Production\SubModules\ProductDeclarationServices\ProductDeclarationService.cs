﻿using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using YwAdmin.Multiplex.Contracts.Consts;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.SqlSugar;
using YwAdmin.Multiplex.Contracts.IAdminUser;
using YwAdmin.Multiplex.Contracts;
using Mapster;
using Volo.Abp;
using YwAdmin.Production.SubModules.BomServices.Dtos;
using YwAdmin.Production.SubModules.ProductDeclarationServices.Input;
using YwAdmin.Production.SubModules.ProductDeclarationServices.Dtos;
using Newtonsoft.Json;

namespace YwAdmin.Production.SubModules.ProductDeclarationServices;

/// <summary>
/// 商品申报要素
/// </summary>
/// <param name="db">数据</param>
/// <param name="currentUser"></param>
/// <param name="userRepository">用户表</param>
/// <param name="productRepository">商品表</param>
/// <param name="productDeclarationRepository">商品申报要素表</param>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.PRODUCTION)]
public class ProductDeclarationService(
    ISqlSugarClient db,
    ICurrentUser currentUser,
    Repository<Items> itemsRepository,
    Repository<UserEntity> userRepository,
    Repository<Material> materialRepository,
     Repository<ProductDeclaration> productDeclarationRepository,
      Repository<ProductCodes> productRepository,
      Repository<ProductDeclarationMaterial> productDeclarationMaterialRepository
    ) : ProductionServiceBase
{
    private readonly Repository<ProductDeclaration> _productDeclarationRepository = productDeclarationRepository;
    private readonly Repository<Items> _itemsRepository = itemsRepository;
    private readonly Repository<ProductDeclarationMaterial> _productDeclarationMaterialRepository = productDeclarationMaterialRepository;
    private readonly Repository<ProductCodes> _productRepository = productRepository;
    private readonly ICurrentUser _currentUserRepository = currentUser;
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<UserEntity> _userRepository = userRepository;
    private readonly Repository<Material> _materialRepository = materialRepository;

    /// <summary>
    /// 查询商品编码数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    public async Task<TableData> GetProductDeclaration(ProductDeclarationInput input)
    {
        var query = _db.Queryable<ProductDeclaration>().Where(x => x.HSCode == input.HSCode);
        var results = await query.OrderBy(x => x.Seq).ToListAsync();
        var resultList = results.Select(
              result =>
              {
                  var dto = result.Adapt<ProductDeclarationOutput>();
                  return dto;
              });
        return new TableData { Data = resultList };
    }

    // <summary>
    // 新增或者更新商品要素表
    // </summary>
    // <param name="input"></param>
    // <returns></returns>
    // <exception cref="UserFriendlyException"></exception>
    //
    [HttpPost]
    public async Task<TableData> AddOrEditProductDeclaration(ProductDeclarationInput input)
    {
        if (string.IsNullOrEmpty(input.HSCode))
        {
            throw new UserFriendlyException(L["DeletedData"]);
        }

        List<AddProductDeclarationDto> itemList = string.IsNullOrEmpty(input.Items) ? new List<AddProductDeclarationDto>() : JsonConvert.DeserializeObject<List<AddProductDeclarationDto>>(input.Items) ?? new List<AddProductDeclarationDto>();
        //先删除所有数据，在进行添加
        await _productDeclarationRepository.DeleteAsync(x => x.HSCode == input.HSCode);
        //解析Items进行新增
        //检查商品编号是否重复

        foreach (var item in itemList)
        {
            var entity = item.Adapt<ProductDeclaration>();
            entity.HSCode = input.HSCode;
            await _productDeclarationRepository.InsertAsync(entity);
        }
        var product = await _productRepository.GetFirstAsync(x => x.HSCode == input.HSCode);
        if (product != null)
        {
            product.ProductDec = input.SpecModel;
            await _productRepository.UpdateAsync(product);
        }
        return new TableData { Data = L["SaveSuccessfule"] };
    }

    /// <summary>
    /// 查询商品编码明细数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    [HttpPost]
    public async Task<TableData> GetProductDeclarationInfo(ProductDeclarationInput input)
    {
        if (input.Id == null | input.Id == 0)
        {
            throw new UserFriendlyException(L["DataException"]);
        }
        var entity = await _db.Queryable<ProductDeclaration>().FirstAsync(x => x.HSCode == input.HSCode);
        var result = entity.Adapt<ProductDeclarationOutput>();

        result.CreateUserName = _userRepository.GetById(entity.CreateUserId)?.Name;
        result.UpdateUserName = _userRepository.GetById(entity.UpdateUserId)?.Name;
        return new TableData
        {
            Data = result,
        };
    }

    /// <summary>
    /// 查询料号级商品要素
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>

    [HttpPost]
    public async Task<TableData> GetProductDecMataial(ProductDeclarationInput input)
    {
        var query = _db.Queryable<ProductDeclarationMaterial>().Where(x => x.HSCode == input.HSCode && x.MCode == input.MCode);
        var results = await query.OrderBy(x => x.Seq).ToListAsync();
        var resultList = results.Select(
              result =>
              {
                  var dto = result.Adapt<ProductDeclarationOutput>();
                  return dto;
              });
        if (resultList.Count() == 0)
        {
            return await GetProductDeclaration(input);
        }
        return new TableData { Data = resultList };
    }

    // <summary>
    // 新增或者更新商品要素表
    // </summary>
    // <param name="input"></param>
    // <returns></returns>
    // <exception cref="UserFriendlyException"></exception>
    //
    [HttpPost]
    public async Task<TableData> AddOrEditMaterialDeclaration(ProductDeclarationInput input)
    {
        if (string.IsNullOrEmpty(input.HSCode))
        {
            throw new UserFriendlyException(L["DeletedData"]);
        }

        List<AddProductDeclarationDto> itemList = string.IsNullOrEmpty(input.Items) ? new List<AddProductDeclarationDto>() : JsonConvert.DeserializeObject<List<AddProductDeclarationDto>>(input.Items) ?? new List<AddProductDeclarationDto>();
        //先删除所有数据，在进行添加
        await _productDeclarationMaterialRepository.DeleteAsync(x => x.HSCode == input.HSCode && x.MCode == input.MCode);
        //解析Items进行新增
        //检查商品编号是否重复

        foreach (var item in itemList)
        {
            var entity = item.Adapt<ProductDeclarationMaterial>();
            entity.HSCode = input.HSCode;
            entity.MCode = input.MCode;
            await _productDeclarationMaterialRepository.InsertAsync(entity);
        }
        //同时保存，当前输入的规格型号数据
        var material = await _materialRepository.GetFirstAsync(x => x.MCode == input.MCode);
        if (material != null)
        {
            material.SpecModel = input.SpecModel;
            await _materialRepository.UpdateAsync(material);
        }
        else
        {
            var items = await _itemsRepository.GetFirstAsync(x => x.FGCode == input.MCode);
            if (items != null)
            {
                items.SpecModel = input.SpecModel;
                await _itemsRepository.UpdateAsync(items);
            }

        }
        return new TableData { Data = L["SaveSuccessfule"] };
    }
}