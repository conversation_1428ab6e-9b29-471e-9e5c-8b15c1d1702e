﻿

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Http.ProxyScripting.Generators;

namespace YwAdmin.Application.RoleServices.Input;
public class RoleInput : PaginationParams
{
    /// <summary>
    /// 角色代码
    /// </summary>
    public string RoleCode { get; set; }
    /// <summary>
    /// 角色代码
    /// </summary>
    public string RoleName { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
    /// <summary>
    /// 描述
    /// </summary>
    public string Desctiption { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    public long RoleId { get; set; }

    public long Id { get; set; }

    public long Ids { get; set; }
    /// <summary>
    /// 真实姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 账号
    /// </summary>
    public string Account { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Telephone { get; set; }

    /// <summary>
    /// 邮件
    /// </summary>
    public string Email { get; set; }
    public string Items { get; set; }
}
