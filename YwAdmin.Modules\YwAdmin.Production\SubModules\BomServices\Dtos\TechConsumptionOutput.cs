
namespace YwAdmin.Production.BomServices.Dtos;
public class TechConsumptionOutput
{
	public long Id { get; set; }
    /// <summary>
	/// 序号
	/// </summary>
	public int? Seq { get; set; }
	/// <summary>
	/// 成品编号
	/// </summary>
	public string FGCode { get; set; }
	/// <summary>
	/// 款号
	/// </summary>
	public string StyleNo { get; set; }
	/// <summary>
	/// 原材料名称
	/// </summary>
	public string MName { get; set; }
	/// <summary>
	/// 原材料款号
	/// </summary>
	public string MCode { get; set; }
	/// <summary>
	/// 内部编号
	/// </summary>
	public string IntCode { get; set; }
	/// <summary>
	/// 规格型号
	/// </summary>
	public string SpecModel { get; set; }
	/// <summary>
	/// 取套
	/// </summary>
	public decimal? Casing { get; set; }
	/// <summary>
	/// 拖布长度
	/// </summary>
	public decimal? MopLength { get; set; }
	/// <summary>
	/// 原始单耗
	/// </summary>
	public decimal? OriginalUnit { get; set; }
	/// <summary>
	/// 损耗率
	/// </summary>
	public decimal? LossRate { get; set; }
	/// <summary>
	/// 单耗
	/// </summary>
	public decimal? UnitConsumption { get; set; }
	/// <summary>
	/// 客供取套
	/// </summary>
	public decimal? CusProvidedKit { get; set; }
	/// <summary>
	/// 客供拖布长度
	/// </summary>
	public decimal? CusProvidedMopClothLength { get; set; }
	/// <summary>
	/// 客供单耗
	/// </summary>
	public decimal? CusProvidUnitConsumption { get; set; }
	/// <summary>
	/// 整版套数
	/// </summary>
	public decimal? FullPageSets { get; set; }
	/// <summary>
	/// 有效使用率
	/// </summary>
	public decimal? EffectiveUsageRate { get; set; }
	public string ErrorMsg { get; set; }
	public bool? HasErrorMsg{ get; set; }
}
